import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Send, Smile, Paperclip, Phone, Video, Search,
  Image, File, MapPin,
  Plus, Reply, Forward, Copy, Delete, Edit, Pin,
  Archive, Settings, Info,
  MessageSquare, Gift,
  CheckCircle2, Circle, X, ChevronLeft,
  Maximize2, Minimize2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

import { getSafeImage } from '@/lib/constants';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import {
  Message, Conversation, MessageAttachment,
  CallData
} from '@/types/enhanced-messaging';

interface FacebookMessagingProps {
  className?: string;
  currentUserId?: string;
  onCall?: (conversationId: string, type: 'audio' | 'video') => void;
  onVideoCall?: (conversationId: string) => void;
}

const FacebookMessaging: React.FC<FacebookMessagingProps> = ({
  className,
  currentUserId = 'user_1',
  onCall,
  onVideoCall: _onVideoCall
}) => {
  // Enhanced messaging hook
  const {
    conversations,
    activeConversation: _activeConversation,
    messages: _messages,
    isConnected: _isConnected,
    isTyping: _isTyping,
    typingUsers: _typingUsers,
    searchResults: _searchResults,
    currentCall: _currentCall,
    settings: _settings,
    selectConversation: _selectConversation,
    sendMessage: _sendMessage,
    editMessage: _editMessage,
    deleteMessage: _deleteMessage,
    forwardMessage: _forwardMessage,
    addReaction: _addReaction,
    searchMessages: _searchMessages,
    clearSearch: _clearSearch,
    startCall: _startCall,
    endCall: _endCall,
    createThread: _createThread,
    replyToThread: _replyToThread,
    saveDraft: _saveDraft,
    getDraft: _getDraft,
    clearDraft: _clearDraft,
    updateSettings: _updateSettings,
    setTyping: _setTyping,
    markAsRead: _markAsRead,
    loadMoreMessages: _loadMoreMessages
  } = useEnhancedMessaging(currentUserId);

  // Local UI state
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, _setIsSearching] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachments, _setShowAttachments] = useState(false);
  const [showStickers, _setShowStickers] = useState(false);
  const [showGifs, _setShowGifs] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [showSearch, _setShowSearch] = useState(false);
  const [showCall, _setShowCall] = useState(false);
  const [showSettings, _setShowSettings] = useState(false);
  
  // Message actions
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [editingMessageId, _setEditingMessageId] = useState<string | null>(null);
  const [forwardingMessage, _setForwardingMessage] = useState<Message | null>(null);
  const [selectedMessages, _setSelectedMessages] = useState<Set<string>>(new Set());
  const [showMessageActions, _setShowMessageActions] = useState(false);
  
  // UI layout
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showSidebar, setShowSidebar] = useState(!isMobile);
  const [sidebarWidth, _setSidebarWidth] = useState(320);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const voiceRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingTimer = useRef<NodeJS.Timeout>();
  
  // Emoji reactions quick select
  const QUICK_REACTIONS = ['👍', '❤️', '😂', '😮', '😢', '😡'];
  
  // Mock data with enhanced features
  const mockConversations: Conversation[] = [
    {
      id: 'conv_1',
      type: 'direct',
      user: {
        id: 'user_2',
        name: 'Sarah Johnson',
        avatar: getSafeImage('AVATARS', 1),
        isOnline: true,
        lastActive: 'Active now',
        isVerified: true,
        username: 'sarah.johnson'
      },
      lastMessage: {
        content: 'Hey! How are you doing? 😊',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        isRead: false,
        senderId: 'user_2'
      },
      unreadCount: 3,
      mentionCount: 1,
      isPinned: true,
      isMuted: false,
      isArchived: false,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 5 * 60 * 1000),
      customEmoji: '🌟',
      color: '#FF6B6B',
      settings: {
        deliveryReceipts: true,
        readReceipts: true,
        typingIndicators: true,
        sound: true,
        notifications: 'all',
        disappearingMessages: { enabled: false, duration: 0 }
      }
    },
    {
      id: 'conv_2',
      type: 'group',
      name: 'Design Team 🎨',
      description: 'UI/UX Design Team Collaboration',
      participants: [
        {
          id: 'user_3',
          name: 'Mike Chen',
          avatar: getSafeImage('AVATARS', 2),
          isOnline: false,
          lastActive: '2h ago',
          username: 'mike.chen'
        },
        {
          id: 'user_4',
          name: 'Emma Davis',
          avatar: getSafeImage('AVATARS', 3),
          isOnline: true,
          lastActive: 'Active now',
          isVerified: true,
          username: 'emma.davis'
        },
        {
          id: 'user_5',
          name: 'Alex Kim',
          avatar: getSafeImage('AVATARS', 4),
          isOnline: true,
          lastActive: 'Active now',
          username: 'alex.kim'
        }
      ],
      lastMessage: {
        content: 'Great work on the new mockups! 🚀',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        isRead: true,
        senderId: 'user_4'
      },
      unreadCount: 0,
      isPinned: false,
      isMuted: false,
      isArchived: false,
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      color: '#4ECDC4',
      settings: {
        deliveryReceipts: true,
        readReceipts: true,
        typingIndicators: true,
        sound: true,
        notifications: 'mentions',
        disappearingMessages: { enabled: false, duration: 0 }
      },
      permissions: {
        canAddMembers: 'all',
        canRemoveMembers: 'admins',
        canEditInfo: 'admins',
        canSendMessages: 'all',
        canDeleteMessages: 'admins',
        requireAdminApproval: false
      }
    }
  ];

  const mockMessages: Message[] = [
    {
      id: 'msg_1',
      conversationId: 'conv_1',
      senderId: 'user_2',
      content: 'Hey there! How was your weekend?',
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      type: 'text',
      status: 'read',
      reactions: [
        { id: 'r1', emoji: '👍', userId: currentUserId, timestamp: new Date() }
      ]
    },
    {
      id: 'msg_2',
      conversationId: 'conv_1',
      senderId: currentUserId,
      content: 'It was amazing! Went hiking in the mountains 🏔️',
      timestamp: new Date(Date.now() - 50 * 60 * 1000),
      type: 'text',
      status: 'read',
      attachments: [
        {
          id: 'att_1',
          type: 'image',
          url: getSafeImage('POSTS', 1),
          name: 'mountain_hike.jpg',
          size: 1024000,
          dimensions: { width: 800, height: 600 }
        }
      ]
    },
    {
      id: 'msg_3',
      conversationId: 'conv_1',
      senderId: 'user_2',
      content: 'Wow! That looks incredible! 😍 I need to plan a hiking trip soon.',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      type: 'text',
      status: 'read',
      replyTo: 'msg_2',
      reactions: [
        { id: 'r2', emoji: '❤️', userId: currentUserId, timestamp: new Date() },
        { id: 'r3', emoji: '🏔️', userId: 'user_2', timestamp: new Date() }
      ]
    }
  ];

  // Initialize data
  useEffect(() => {
    setConversations(mockConversations);
    setMessages(mockMessages);
    if (mockConversations.length > 0) {
      setSelectedConversation(mockConversations[0]);
    }
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      setShowSidebar(!mobile);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Voice recording timer
  useEffect(() => {
    if (isRecording) {
      recordingTimer.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      setRecordingTime(0);
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, [isRecording]);

  // Filtered conversations
  const filteredConversations = useMemo(() => {
    return conversations.filter(conversation => {
      const searchTerm = searchQuery.toLowerCase();
      const name = conversation.type === 'group' 
        ? conversation.name 
        : conversation.user?.name;
      const lastMessageContent = conversation.lastMessage?.content || '';
      
      return name?.toLowerCase().includes(searchTerm) ||
             lastMessageContent.toLowerCase().includes(searchTerm);
    });
  }, [conversations, searchQuery]);

  // Current conversation messages
  const conversationMessages = useMemo(() => {
    return selectedConversation 
      ? messages.filter(msg => msg.conversationId === selectedConversation.id)
      : [];
  }, [messages, selectedConversation]);

  // Utility functions
  const formatTime = useCallback((timestamp: Date | string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    if (days < 7) return `${days}d`;
    return date.toLocaleDateString();
  }, []);

  const formatRecordingTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Message handlers
  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim() || !selectedConversation) return;

    const message: Message = {
      id: `msg_${Date.now()}`,
      conversationId: selectedConversation.id,
      senderId: currentUserId,
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
      status: 'sending',
      ...(replyingTo && { replyTo: replyingTo.id })
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
    setReplyingTo(null);
    
    // Update conversation last message
    setConversations(prev => prev.map(conv => 
      conv.id === selectedConversation.id 
        ? { 
            ...conv, 
            lastMessage: {
              content: message.content,
              timestamp: message.timestamp.toISOString(),
              isRead: true,
              senderId: message.senderId
            },
            updatedAt: message.timestamp 
          }
        : conv
    ));

    // Simulate message status updates
    setTimeout(() => {
      setMessages(prev => prev.map(msg => 
        msg.id === message.id ? { ...msg, status: 'sent' } : msg
      ));
    }, 1000);

    setTimeout(() => {
      setMessages(prev => prev.map(msg => 
        msg.id === message.id ? { ...msg, status: 'delivered' } : msg
      ));
    }, 2000);
  }, [newMessage, selectedConversation, currentUserId, replyingTo]);

  const handleReaction = useCallback((messageId: string, emoji: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const reactions = msg.reactions || [];
        const existingReaction = reactions.find(r => r.userId === currentUserId);
        
        if (existingReaction) {
          if (existingReaction.emoji === emoji) {
            // Remove reaction
            return {
              ...msg,
              reactions: reactions.filter(r => r.userId !== currentUserId)
            };
          } else {
            // Update reaction
            return {
              ...msg,
              reactions: reactions.map(r => 
                r.userId === currentUserId 
                  ? { ...r, emoji, timestamp: new Date() }
                  : r
              )
            };
          }
        } else {
          // Add new reaction
          return {
            ...msg,
            reactions: [
              ...reactions,
              {
                id: `r_${Date.now()}`,
                emoji,
                userId: currentUserId,
                timestamp: new Date()
              }
            ]
          };
        }
      }
      return msg;
    }));
  }, [currentUserId]);

  const handleStartCall = useCallback((type: 'audio' | 'video') => {
    if (!selectedConversation) return;
    
    const callData: CallData = {
      id: `call_${Date.now()}`,
      conversationId: selectedConversation.id,
      type,
      participants: [currentUserId, ...(selectedConversation.participants?.map(p => p.id) || [selectedConversation.user?.id!])],
      status: 'ringing',
      initiatorId: currentUserId,
      startedAt: new Date()
    };

    setActiveCall(callData);
    
    if (onCall) {
      onCall(selectedConversation.id, type);
    }
    
    toast.success(`${type === 'video' ? 'Video' : 'Audio'} call started`);
  }, [selectedConversation, currentUserId, onCall]);

  const handleFileUpload = useCallback((type: 'image' | 'file' | 'video') => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = type === 'image' ? 'image/*' : 
                                   type === 'video' ? 'video/*' : '*/*';
      fileInputRef.current.click();
    }
  }, []);

  const handleVoiceRecord = useCallback(async () => {
    if (isRecording) {
      // Stop recording
      if (voiceRecorderRef.current) {
        voiceRecorderRef.current.stop();
      }
      setIsRecording(false);
    } else {
      // Start recording
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const mediaRecorder = new MediaRecorder(stream);
        voiceRecorderRef.current = mediaRecorder;
        
        mediaRecorder.start();
        setIsRecording(true);
        
        mediaRecorder.ondataavailable = (event) => {
          // Handle recorded audio data
          console.log('Recording data available:', event.data);
        };
        
        mediaRecorder.onstop = () => {
          stream.getTracks().forEach(track => track.stop());
          toast.success('Voice message recorded');
        };
      } catch (error) {
        toast.error('Could not access microphone');
      }
    }
  }, [isRecording]);

  const MessageBubble = React.memo(({ message, isOwn }: { message: Message; isOwn: boolean }) => {
    const replyToMessage = message.replyTo 
      ? conversationMessages.find(m => m.id === message.replyTo)
      : null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-4 group`}
      >
        <div className={`max-w-[70%] ${isOwn ? 'order-2' : 'order-1'}`}>
          {/* Reply preview */}
          {replyToMessage && (
            <div className={`mb-2 p-2 rounded-lg border-l-4 bg-gray-50 dark:bg-gray-800 text-sm ${
              isOwn ? 'border-blue-500' : 'border-gray-300'
            }`}>
              <p className="font-medium text-xs text-gray-500 mb-1">
                Replying to {replyToMessage.senderId === currentUserId ? 'yourself' : 'message'}
              </p>
              <p className="text-gray-600 dark:text-gray-400 truncate">
                {replyToMessage.content}
              </p>
            </div>
          )}

          {/* Message content */}
          <div className={`relative rounded-2xl px-4 py-2 ${
            isOwn 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
          }`}>
            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <div className="mb-2">
                {message.attachments.map(attachment => (
                  <div key={attachment.id} className="mb-2">
                    {attachment.type === 'image' && (
                      <img
                        src={attachment.url}
                        alt={attachment.name}
                        className="max-w-full h-auto rounded-lg"
                        style={{ maxHeight: '300px' }}
                      />
                    )}
                    {attachment.type === 'file' && (
                      <div className="flex items-center space-x-2 p-2 bg-white bg-opacity-20 rounded-lg">
                        <File className="w-6 h-6" />
                        <div>
                          <p className="font-medium">{attachment.name}</p>
                          <p className="text-xs opacity-75">
                            {(attachment.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Text content */}
            {message.content && (
              <p className="break-words">{message.content}</p>
            )}

            {/* Message info */}
            <div className="flex items-center justify-between mt-2 text-xs opacity-75">
              <span>{formatTime(message.timestamp)}</span>
              {isOwn && (
                <div className="flex items-center space-x-1">
                  {message.isEdited && <span>(edited)</span>}
                  <span>
                    {message.status === 'sending' && <Circle className="w-3 h-3" />}
                    {message.status === 'sent' && <CheckCircle2 className="w-3 h-3" />}
                    {message.status === 'delivered' && <div className="flex"><CheckCircle2 className="w-3 h-3" /><CheckCircle2 className="w-3 h-3 -ml-1" /></div>}
                    {message.status === 'read' && <div className="flex text-blue-300"><CheckCircle2 className="w-3 h-3" /><CheckCircle2 className="w-3 h-3 -ml-1" /></div>}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex items-center space-x-1 mt-1">
              {QUICK_REACTIONS.map(emoji => {
                const reaction = message.reactions?.find(r => r.emoji === emoji);
                const count = message.reactions?.filter(r => r.emoji === emoji).length || 0;
                if (count === 0) return null;
                
                return (
                  <button
                    key={emoji}
                    onClick={() => handleReaction(message.id, emoji)}
                    className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                      reaction?.userId === currentUserId
                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                        : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                  >
                    <span>{emoji}</span>
                    {count > 1 && <span>{count}</span>}
                  </button>
                );
              })}
            </div>
          )}

          {/* Quick reaction buttons (on hover) */}
          <div className="flex items-center space-x-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
            {QUICK_REACTIONS.map(emoji => (
              <button
                key={emoji}
                onClick={() => handleReaction(message.id, emoji)}
                className="w-8 h-8 rounded-full bg-white dark:bg-gray-700 shadow-md flex items-center justify-center hover:scale-110 transition-transform"
              >
                {emoji}
              </button>
            ))}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="w-8 h-8 rounded-full bg-white dark:bg-gray-700 shadow-md flex items-center justify-center hover:scale-110 transition-transform">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setReplyingTo(message)}>
                  <Reply className="w-4 h-4 mr-2" />
                  Reply
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setForwardingMessage(message)}>
                  <Forward className="w-4 h-4 mr-2" />
                  Forward
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy
                </DropdownMenuItem>
                {isOwn && (
                  <>
                    <DropdownMenuItem onClick={() => setEditingMessage(message)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <Delete className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Avatar for received messages */}
        {!isOwn && (
          <Avatar className={`w-8 h-8 ${isOwn ? 'order-1 ml-2' : 'order-2 mr-2'}`}>
            <AvatarImage src={
              selectedConversation?.type === 'group'
                ? selectedConversation.participants?.find(p => p.id === message.senderId)?.avatar
                : selectedConversation?.user?.avatar
            } />
            <AvatarFallback>
              {selectedConversation?.type === 'group'
                ? selectedConversation.participants?.find(p => p.id === message.senderId)?.name[0]
                : selectedConversation?.user?.name[0]
              }
            </AvatarFallback>
          </Avatar>
        )}
      </motion.div>
    );
  });

  return (
    <TooltipProvider>
      <div className={`flex h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
        {/* Sidebar */}
        <AnimatePresence>
          {showSidebar && (
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: sidebarWidth }}
              exit={{ width: 0 }}
              className="border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex flex-col"
              style={{ minWidth: sidebarWidth }}
            >
              {/* Sidebar Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">Messages</h1>
                  <div className="flex items-center space-x-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>New message</TooltipContent>
                    </Tooltip>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Settings className="w-4 h-4 mr-2" />
                          Settings
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Archive className="w-4 h-4 mr-2" />
                          Archived
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Filter className="w-4 h-4 mr-2" />
                          Filter
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search conversations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>
              </div>

              {/* Conversation List */}
              <ScrollArea className="flex-1">
                <div className="space-y-1 p-2">
                  {filteredConversations.map((conversation) => {
                    const isSelected = selectedConversation?.id === conversation.id;
                    const displayName = conversation.type === 'group' 
                      ? conversation.name 
                      : conversation.user?.name;
                    const displayAvatar = conversation.type === 'group'
                      ? getSafeImage('AVATARS', 0) // Group default
                      : conversation.user?.avatar;
                    const isOnline = conversation.type === 'direct' 
                      ? conversation.user?.isOnline 
                      : false;

                    return (
                      <motion.div
                        key={conversation.id}
                        whileHover={{ backgroundColor: 'rgba(0,0,0,0.05)' }}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          isSelected 
                            ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                            : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                        onClick={() => setSelectedConversation(conversation)}
                      >
                        <div className="flex items-center space-x-3">
                          {/* Avatar with online indicator */}
                          <div className="relative">
                            <Avatar className="w-12 h-12">
                              <AvatarImage src={displayAvatar} />
                              <AvatarFallback>{displayName?.[0]}</AvatarFallback>
                            </Avatar>
                            {isOnline && (
                              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
                            )}
                            {conversation.user?.isVerified && (
                              <div className="absolute -top-1 -right-1">
                                <CheckCircle2 className="w-4 h-4 text-blue-500 bg-white rounded-full" />
                              </div>
                            )}
                          </div>

                          {/* Conversation info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <h3 className={`font-medium truncate ${
                                  conversation.unreadCount > 0 ? 'font-bold' : ''
                                } text-gray-900 dark:text-white`}>
                                  {displayName}
                                </h3>
                                {conversation.isPinned && (
                                  <Pin className="w-3 h-3 text-yellow-500 fill-current" />
                                )}
                                {conversation.isMuted && (
                                  <VolumeX className="w-3 h-3 text-gray-400" />
                                )}
                                {conversation.customEmoji && (
                                  <span className="text-sm">{conversation.customEmoji}</span>
                                )}
                              </div>
                              <div className="flex items-center space-x-1">
                                {conversation.lastMessage && (
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {formatTime(conversation.lastMessage.timestamp)}
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center justify-between mt-1">
                              <p className={`text-sm truncate ${
                                conversation.unreadCount > 0 
                                  ? 'font-medium text-gray-900 dark:text-white' 
                                  : 'text-gray-500 dark:text-gray-400'
                              }`}>
                                {conversation.type === 'group' && conversation.lastMessage?.senderId !== currentUserId && (
                                  <span className="font-medium">
                                    {conversation.participants?.find(p => p.id === conversation.lastMessage?.senderId)?.name}: 
                                  </span>
                                )}
                                {conversation.lastMessage?.content || 'No messages yet'}
                              </p>
                              <div className="flex items-center space-x-1">
                                {conversation.unreadCount > 0 && (
                                  <Badge className="bg-blue-600 text-white text-xs min-w-[20px] h-5">
                                    {conversation.unreadCount}
                                  </Badge>
                                )}
                                {conversation.mentionCount && conversation.mentionCount > 0 && (
                                  <Badge className="bg-red-600 text-white text-xs min-w-[20px] h-5">
                                    @{conversation.mentionCount}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedConversation ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* Mobile back button */}
                    {isMobile && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowSidebar(true)}
                        className="md:hidden"
                      >
                        <ChevronLeft className="w-5 h-5" />
                      </Button>
                    )}

                    {/* Conversation avatar and info */}
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={
                        selectedConversation.type === 'group'
                          ? getSafeImage('AVATARS', 0)
                          : selectedConversation.user?.avatar
                      } />
                      <AvatarFallback>
                        {selectedConversation.type === 'group'
                          ? selectedConversation.name?.[0]
                          : selectedConversation.user?.name[0]
                        }
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {selectedConversation.type === 'group'
                            ? selectedConversation.name
                            : selectedConversation.user?.name
                          }
                        </h3>
                        {selectedConversation.user?.isVerified && (
                          <CheckCircle2 className="w-4 h-4 text-blue-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedConversation.type === 'group'
                          ? `${selectedConversation.participants?.length || 0} members`
                          : selectedConversation.user?.isOnline
                            ? 'Active now'
                            : selectedConversation.user?.lastActive
                        }
                      </p>
                    </div>
                  </div>

                  {/* Header actions */}
                  <div className="flex items-center space-x-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStartCall('audio')}
                        >
                          <Phone className="w-5 h-5" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Audio call</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStartCall('video')}
                        >
                          <Video className="w-5 h-5" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Video call</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsFullscreen(!isFullscreen)}
                        >
                          {isFullscreen ? <Minimize2 className="w-5 h-5" /> : <Maximize2 className="w-5 h-5" />}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}</TooltipContent>
                    </Tooltip>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-5 h-5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Info className="w-4 h-4 mr-2" />
                          Conversation info
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Search className="w-4 h-4 mr-2" />
                          Search in conversation
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Pin className="w-4 h-4 mr-2" />
                          Pinned messages
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Mute className="w-4 h-4 mr-2" />
                          Mute notifications
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Archive className="w-4 h-4 mr-2" />
                          Archive conversation
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Delete className="w-4 h-4 mr-2" />
                          Delete conversation
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  <AnimatePresence>
                    {conversationMessages.map((message) => {
                      const isOwn = message.senderId === currentUserId;
                      return (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          isOwn={isOwn}
                        />
                      );
                    })}
                  </AnimatePresence>
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Reply Preview */}
              <AnimatePresence>
                {replyingTo && (
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: 0 }}
                    className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-1 h-8 bg-blue-500 rounded" />
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Replying to {replyingTo.senderId === currentUserId ? 'yourself' : 'message'}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-96">
                            {replyingTo.content}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setReplyingTo(null)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Message Input */}
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                <div className="flex items-end space-x-2">
                  {/* Attachment button */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                        <Plus className="w-5 h-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleFileUpload('image')}>
                        <Image className="w-4 h-4 mr-2" />
                        Photo
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleFileUpload('file')}>
                        <File className="w-4 h-4 mr-2" />
                        File
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleFileUpload('video')}>
                        <Video className="w-4 h-4 mr-2" />
                        Video
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <MapPin className="w-4 h-4 mr-2" />
                        Location
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Gift className="w-4 h-4 mr-2" />
                        GIF
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Smile className="w-4 h-4 mr-2" />
                        Sticker
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Message input */}
                  <div className="flex-1 relative">
                    <Textarea
                      ref={messageInputRef}
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey && chatSettings.enterToSend) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      className="min-h-[40px] max-h-[120px] resize-none pr-12"
                      rows={1}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    >
                      <Smile className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Voice/Send button */}
                  {newMessage.trim() ? (
                    <Button
                      onClick={handleSendMessage}
                      className="h-10 w-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  ) : (
                    <Button
                      variant={isRecording ? "destructive" : "ghost"}
                      size="sm"
                      className="h-10 w-10 p-0"
                      onClick={handleVoiceRecord}
                      onMouseDown={() => !isRecording && handleVoiceRecord()}
                    >
                      <Mic className={`w-5 h-5 ${isRecording ? 'animate-pulse' : ''}`} />
                    </Button>
                  )}
                </div>

                {/* Recording indicator */}
                <AnimatePresence>
                  {isRecording && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="flex items-center justify-center space-x-2 mt-2 text-red-600"
                    >
                      <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse" />
                      <span className="text-sm font-medium">
                        Recording {formatRecordingTime(recordingTime)}
                      </span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </>
          ) : (
            /* Empty state */
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Select a conversation
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              console.log('File selected:', file);
              // Handle file upload
            }
          }}
        />
      </div>
    </TooltipProvider>
  );
};

export default FacebookMessaging;
