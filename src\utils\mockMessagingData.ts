import { User, Conversation, Message } from '@/types/enhanced-messaging';

// Dummy users with realistic data
export const mockUsers: User[] = [
  {
    id: 'user_1',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b332c1e6?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastActive: '5 minutes ago',
    lastSeen: new Date(Date.now() - 5 * 60 * 1000),
    isVerified: true,
    username: 'sarah<PERSON>'
  },
  {
    id: 'user_2',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastActive: '2 minutes ago',
    lastSeen: new Date(Date.now() - 2 * 60 * 1000),
    isVerified: false,
    username: 'mchen'
  },
  {
    id: 'user_3',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastActive: '2 hours ago',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isVerified: true,
    username: 'emily_r'
  },
  {
    id: 'user_4',
    name: 'David Wilson',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastActive: '1 minute ago',
    lastSeen: new Date(Date.now() - 1 * 60 * 1000),
    isVerified: false,
    username: 'dwilson'
  },
  {
    id: 'user_5',
    name: 'Jessica Taylor',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastActive: '8 hours ago',
    lastSeen: new Date(Date.now() - 8 * 60 * 60 * 1000),
    isVerified: true,
    username: 'jtaylor'
  },
  {
    id: 'user_6',
    name: 'Alex Thompson',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastActive: '10 minutes ago',
    lastSeen: new Date(Date.now() - 10 * 60 * 1000),
    isVerified: false,
    username: 'athompson'
  },
  {
    id: 'user_7',
    name: 'Rachel Green',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastActive: '4 hours ago',
    lastSeen: new Date(Date.now() - 4 * 60 * 60 * 1000),
    isVerified: true,
    username: 'rgreen'
  },
  {
    id: 'user_8',
    name: 'Tom Anderson',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastActive: 'Just now',
    lastSeen: new Date(Date.now() - 30 * 1000),
    isVerified: false,
    username: 'tanderson'
  }
];

// Mock conversations with themes
export const createMockConversations = (currentUserId: string): Conversation[] => {
  const now = new Date();
  
  return [
    {
      id: 'conv_1',
      type: 'direct',
      user: mockUsers[0], // Sarah Johnson
      lastMessage: {
        content: 'Found this cool café downtown! ☕',
        timestamp: new Date(now.getTime() - 15 * 60 * 1000).toISOString(),
        isRead: false,
        senderId: 'user_1'
      },
      unreadCount: 2,
      isMuted: false,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'sunset',
        name: 'Sunset',
        colors: {
          primary: '#FF6B35',
          secondary: '#F7931E',
          accent: '#FFD700',
          background: '#FFF8DC'
        }
      },
      createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      customEmoji: '☕'
    },
    {
      id: 'conv_2',
      type: 'direct',
      user: mockUsers[1], // Michael Chen
      lastMessage: {
        content: 'Mainly the JWT implementation and the password reset flow. Thanks! 🙏',
        timestamp: new Date(now.getTime() - 45 * 60 * 1000).toISOString(),
        isRead: true,
        senderId: 'user_2'
      },
      unreadCount: 0,
      isMuted: false,
      isPinned: true,
      isArchived: false,
      theme: {
        id: 'ocean',
        name: 'Ocean',
        colors: {
          primary: '#0066CC',
          secondary: '#4A90E2',
          accent: '#87CEEB',
          background: '#E6F3FF'
        }
      },
      createdAt: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'conv_3',
      type: 'group',
      name: 'Work Squad',
      participants: [mockUsers[1], mockUsers[2], mockUsers[3]], // Michael, Emily, David
      lastMessage: {
        content: 'Team dinner tomorrow at 7? I found this great Italian place! 🍝',
        timestamp: new Date(now.getTime() - 30 * 60 * 1000).toISOString(),
        isRead: false,
        senderId: 'user_4'
      },
      unreadCount: 1,
      isMuted: false,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'forest',
        name: 'Forest',
        colors: {
          primary: '#228B22',
          secondary: '#32CD32',
          accent: '#90EE90',
          background: '#F0FFF0'
        }
      },
      createdAt: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      description: 'Our awesome work team chat',
      customEmoji: '💼'
    },
    {
      id: 'conv_4',
      type: 'direct',
      user: mockUsers[2], // Emily Rodriguez
      lastMessage: {
        content: 'Thanks for the design feedback! I\'ll implement those changes.',
        timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(),
        isRead: true,
        senderId: 'user_3'
      },
      unreadCount: 0,
      isMuted: false,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'cherry',
        name: 'Cherry Blossom',
        colors: {
          primary: '#FFB6C1',
          secondary: '#FFC0CB',
          accent: '#FF69B4',
          background: '#FFF0F5'
        }
      },
      createdAt: new Date(now.getTime() - 21 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'conv_5',
      type: 'group',
      name: 'Movie Buddies',
      participants: [mockUsers[4], mockUsers[5]], // Jessica, Alex
      lastMessage: {
        content: 'Movie night this weekend? 🎬',
        timestamp: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(),
        isRead: false,
        senderId: 'user_6'
      },
      unreadCount: 3,
      isMuted: true,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'purple',
        name: 'Purple',
        colors: {
          primary: '#8A2BE2',
          secondary: '#9370DB',
          accent: '#DA70D6',
          background: '#F8F4FF'
        }
      },
      createdAt: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000),
      description: 'For our weekly movie nights!',
      customEmoji: '🍿'
    },
    {
      id: 'conv_6',
      type: 'direct',
      user: mockUsers[6], // Rachel Green
      lastMessage: {
        content: 'See you at the yoga class!',
        timestamp: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(),
        isRead: true,
        senderId: 'current_user'
      },
      unreadCount: 0,
      isMuted: false,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'mint',
        name: 'Mint',
        colors: {
          primary: '#00FA9A',
          secondary: '#98FB98',
          accent: '#00FF7F',
          background: '#F0FFF0'
        }
      },
      createdAt: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'conv_7',
      type: 'direct',
      user: mockUsers[7], // Tom Anderson
      lastMessage: {
        content: 'Hey! Long time no see 👋',
        timestamp: new Date(now.getTime() - 12 * 60 * 60 * 1000).toISOString(),
        isRead: false,
        senderId: 'user_8'
      },
      unreadCount: 1,
      isMuted: false,
      isPinned: false,
      isArchived: false,
      theme: {
        id: 'default',
        name: 'Default',
        colors: {
          primary: '#1877F2',
          secondary: '#42A5F5',
          accent: '#64B5F6',
          background: '#FFFFFF'
        }
      },
      createdAt: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    }
  ];
};

// Mock messages for conversations
export const createMockMessages = (conversationId: string, currentUserId: string): Message[] => {
  const availableUsers = mockUsers;
  const now = new Date();

  const messageSets: Record<string, Message[]> = {
    'conv_1': [
      {
        id: 'msg_1',
        conversationId: 'conv_1',
        senderId: availableUsers[0].id,
        content: "Hey there! How's your day going? 😊",
        timestamp: new Date(now.getTime() - 60 * 60 * 1000), // 1 hour ago
        type: 'text',
        status: 'delivered',
        reactions: []
      },
      {
        id: 'msg_2',
        conversationId: 'conv_1',
        senderId: currentUserId,
        content: "Pretty good! Just finished a big project. How about you?",
        timestamp: new Date(now.getTime() - 45 * 60 * 1000), // 45 minutes ago
        type: 'text',
        status: 'read',
        reactions: [
          { id: 'react_1', emoji: '👍', userId: availableUsers[0].id, timestamp: new Date(now.getTime() - 40 * 60 * 1000) }
        ]
      },
      {
        id: 'msg_3',
        conversationId: 'conv_1',
        senderId: availableUsers[0].id,
        content: "That's awesome! I'm working on some new marketing campaigns. Want to grab coffee later?",
        timestamp: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
        type: 'text',
        status: 'delivered',
        reactions: []
      },
      {
        id: 'msg_4',
        conversationId: 'conv_1',
        senderId: availableUsers[0].id,
        content: "Found this cool café downtown! ☕",
        timestamp: new Date(now.getTime() - 15 * 60 * 1000), // 15 minutes ago
        type: 'text',
        status: 'delivered',
        reactions: [],
        attachments: [
          {
            id: 'att_1',
            type: 'image',
            url: 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=400&h=300&fit=crop',
            name: 'cool_cafe.jpg',
            size: 245760,
            mimeType: 'image/jpeg'
          }
        ]
      }
    ],
    'conv_2': [
      {
        id: 'msg_5',
        conversationId: 'conv_2',
        senderId: availableUsers[1].id,
        content: "Just pushed the latest changes to the repo. Can you review the new authentication system?",
        timestamp: new Date(now.getTime() - 90 * 60 * 1000), // 1.5 hours ago
        type: 'text',
        status: 'read',
        reactions: []
      },
      {
        id: 'msg_6',
        conversationId: 'conv_2',
        senderId: currentUserId,
        content: "Sure! I'll take a look now. Any specific areas you want me to focus on?",
        timestamp: new Date(now.getTime() - 75 * 60 * 1000), // 1.25 hours ago
        type: 'text',
        status: 'read',
        reactions: []
      },
      {
        id: 'msg_7',
        conversationId: 'conv_2',
        senderId: availableUsers[1].id,
        content: "Mainly the JWT implementation and the password reset flow. Thanks!",
        timestamp: new Date(now.getTime() - 45 * 60 * 1000), // 45 minutes ago
        type: 'text',
        status: 'delivered',
        reactions: [
          { id: 'react_2', emoji: '👍', userId: currentUserId, timestamp: new Date(now.getTime() - 40 * 60 * 1000) }
        ]
      }
    ],
    'conv_3': [
      {
        id: 'msg_8',
        conversationId: 'conv_3',
        senderId: availableUsers[1].id,
        content: "Morning team! Ready for today's sprint planning? 🚀",
        timestamp: new Date(now.getTime() - 4 * 60 * 60 * 1000), // 4 hours ago
        type: 'text',
        status: 'read',
        reactions: [
          { id: 'react_3', emoji: '💪', userId: currentUserId, timestamp: new Date(now.getTime() - 3.5 * 60 * 60 * 1000) },
          { id: 'react_4', emoji: '🎯', userId: availableUsers[2].id, timestamp: new Date(now.getTime() - 3.5 * 60 * 60 * 1000) }
        ]
      },
      {
        id: 'msg_9',
        conversationId: 'conv_3',
        senderId: availableUsers[2].id,
        content: "Great work on the presentation today! 👏 The client loved the new designs",
        timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
        type: 'text',
        status: 'delivered',
        reactions: [
          { id: 'react_5', emoji: '🎉', userId: currentUserId, timestamp: new Date(now.getTime() - 1.5 * 60 * 60 * 1000) },
          { id: 'react_6', emoji: '🔥', userId: availableUsers[3].id, timestamp: new Date(now.getTime() - 1.5 * 60 * 60 * 1000) }
        ]
      },
      {
        id: 'msg_10',
        conversationId: 'conv_3',
        senderId: availableUsers[3].id,
        content: "Team dinner tomorrow at 7? I found this great Italian place! 🍝",
        timestamp: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
        type: 'text',
        status: 'delivered',
        reactions: [],
        metadata: {
          location: {
            name: "Luigi's Italian Restaurant",
            address: "123 Main St, Downtown",
            latitude: 40.7128,
            longitude: -74.0060
          }
        }
      }
    ]
  };

  return messageSets[conversationId] || [];
};

// Function to get a user by ID
export const getUserById = (userId: string): User | undefined => {
  return mockUsers.find(user => user.id === userId);
};

// Function to get online users
export const getOnlineUsers = (): User[] => {
  return mockUsers.filter(user => user.isOnline);
};

// Function to search users by name
export const searchUsers = (query: string): User[] => {
  const lowerQuery = query.toLowerCase();
  return mockUsers.filter(user => 
    user.name.toLowerCase().includes(lowerQuery) ||
    user.avatar.toLowerCase().includes(lowerQuery)
  );
};
