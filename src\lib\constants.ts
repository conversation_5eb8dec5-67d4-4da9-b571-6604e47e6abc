// Application constants and configuration
export const APP_CONFIG = {
  name: 'Facebook Clone',
  version: '2.0.1',
  description: 'High-performance social media platform',
  author: 'Social Media Team',
  
  // Performance settings
  POSTS_PER_PAGE: 10,
  INFINITE_SCROLL_THRESHOLD: 100,
  DEBOUNCE_DELAY: 200,
  CACHE_TIME: 5 * 60 * 1000, // 5 minutes
  
  // UI settings
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DES<PERSON>TOP_BREAKPOINT: 1280,
  
  // Feature flags
  FEATURES: {
    VIRTUAL_SCROLLING: true,
    INFINITE_SCROLL: true,
    REAL_TIME_UPDATES: true,
    OFFLINE_SUPPORT: false,
    ANALYTICS: true,
    STORIES: true,
    LIVE_CHAT: true,
    ACTIVITY_FEED: true,
    TRENDING_TOPICS: true,
    LIVE_STREAMING: true,
    ADVANCED_SEARCH: true,
    EVENT_CALENDAR: true,
    REELS: true,
    MARKETPLACE: true,
    GROUPS: true,
    FRIEND_SUGGESTIONS: true,
    WEATHER_WIDGET: true,
    POLLS: true,
    MEMORIES: true,
    GAMING: true,
    SAVED_ITEMS: true,
    PAGES: true
  }
} as const;

export const ROUTES = {
  HOME: '/',
  AUTH: '/auth',
  PROFILE: '/profile',
  FRIENDS: '/friends',
  MESSAGES: '/messages',
  NOTIFICATIONS: '/notifications',
  WATCH: '/watch',
  MARKETPLACE: '/marketplace',
  GROUPS: '/groups',
  EVENTS: '/events',
  SAVED: '/saved',
  MEMORIES: '/memories',
  SETTINGS: '/settings',
  GAMING: '/gaming',
  SEARCH: '/search',
  PAGES: '/pages',
  RECENT: '/recent',
  REELS: '/reels',
  WEATHER: '/weather',
  DATING: '/dating',
  JOBS: '/jobs',
  BUSINESS: '/business',
  LIVE: '/live'
} as const;

export const STORAGE_KEYS = {
  LAST_REELS_OPEN: 'lastReelsOpen',
  REEL_LIKES: 'reelLikes',
  SAVED_REELS: 'savedReels',
  SAVED_POSTS: 'savedPosts',
  WATCH_HISTORY: 'watchHistory',
  THEME: 'theme',
  EVENTS_VIEW_PREFERENCE: 'eventsViewPreference',
  SAVED_ITEMS: 'savedItems',
  SAVED_VIEW_MODE: 'savedViewMode',
  USER_STORIES: 'userStories',
  POLL_VOTES: 'pollVotes',
  SEARCH_HISTORY: 'searchHistory',
  USER_PREFERENCES: 'userPreferences',
  NOTIFICATIONS_SETTINGS: 'notificationsSettings',
  CHAT_HISTORY: 'chatHistory',
  LAST_ACTIVE: 'lastActive'
} as const;

export const MOCK_IMAGES = {
  AVATARS: [
    'https://picsum.photos/400/400?random=1',
    'https://picsum.photos/400/400?random=2',
    'https://picsum.photos/400/400?random=3',
    'https://picsum.photos/400/400?random=4',
    'https://picsum.photos/400/400?random=5',
    'https://picsum.photos/400/400?random=6',
    'https://picsum.photos/400/400?random=7'
  ],
  POSTS: [
    'https://picsum.photos/800/600?random=10',
    'https://picsum.photos/800/600?random=11',
    'https://picsum.photos/800/600?random=12',
    'https://picsum.photos/800/600?random=13',
    'https://picsum.photos/800/600?random=14',
    'https://picsum.photos/800/600?random=15'
  ],
  COVERS: [
    'https://picsum.photos/1200/400?random=20',
    'https://picsum.photos/1200/400?random=21',
    'https://picsum.photos/1200/400?random=22'
  ],
  EVENTS: [
    'https://picsum.photos/800/600?random=30',
    'https://picsum.photos/800/600?random=31'
  ],
  PROFILE_1: 'https://picsum.photos/800/800?random=40',
  PROFILE_2: 'https://picsum.photos/800/800?random=41',
  PROFILE_3: 'https://picsum.photos/800/800?random=42',
  PROFILE_4: 'https://picsum.photos/800/800?random=43',
  COMPANY_1: 'https://picsum.photos/400/400?random=50',
  COMPANY_2: 'https://picsum.photos/400/400?random=51',
  COMPANY_3: 'https://picsum.photos/400/400?random=52'
} as const;

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
} as const;

export const NOTIFICATION_TYPES = {
  LIKE: 'like',
  COMMENT: 'comment',
  SHARE: 'share',
  FRIEND_REQUEST: 'friend_request',
  MESSAGE: 'message',
  MENTION: 'mention',
  EVENT: 'event',
  BIRTHDAY: 'birthday'
} as const;

export const POST_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  VIDEO: 'video',
  LINK: 'link',
  POLL: 'poll',
  EVENT: 'event',
  LIVE: 'live'
} as const;

export const PRIVACY_SETTINGS = {
  PUBLIC: 'public',
  FRIENDS: 'friends',
  FRIENDS_EXCEPT: 'friends_except',
  SPECIFIC_FRIENDS: 'specific_friends',
  ONLY_ME: 'only_me'
} as const;

// Helper function to safely access MOCK_IMAGES arrays
export const getSafeImage = (type: 'AVATARS' | 'POSTS' | 'COVERS' | 'EVENTS', index: number): string => {
  const fallbackImages = {
    AVATARS: 'https://picsum.photos/400/400?random=100',
    POSTS: 'https://picsum.photos/800/600?random=101',
    COVERS: 'https://picsum.photos/1200/400?random=102',
    EVENTS: 'https://picsum.photos/800/600?random=103'
  };
  
  if (!MOCK_IMAGES || !MOCK_IMAGES[type] || !Array.isArray(MOCK_IMAGES[type])) {
    return fallbackImages[type];
  }
  
  const array = MOCK_IMAGES[type];
  if (index < 0 || index >= array.length) {
    return array[0] || fallbackImages[type];
  }
  
  return array[index] || fallbackImages[type];
};