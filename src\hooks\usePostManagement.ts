import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { MOCK_IMAGES } from '@/lib/constants';

// Extended Post interface for local use
interface ExtendedPost {
  id: string;
  user_id: string;
  content: string;
  image_url?: string;
  video_url?: string;
  created_at: string;
  updated_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  user_has_saved?: boolean;
  location?: string;
  type?: 'text' | 'photo' | 'video';
}

// Constants
const MOCK_USERS = [
  { id: 'user-1', name: '<PERSON>', avatar: MOCK_IMAGES.AVATARS[0] },
  { id: 'user-2', name: '<PERSON>', avatar: MOCK_IMAGES.AVATARS[1] },
  { id: 'user-3', name: '<PERSON>', avatar: MOCK_IMAGES.AVATARS[2] },
  { id: 'user-4', name: '<PERSON>', avatar: MOCK_IMAGES.AVATARS[3] },
  { id: 'user-5', name: '<PERSON>', avatar: MOCK_IMAGES.AVATARS[4] },
];

const MOCK_CONTENTS = [
  'Just shared a new photo from my recent trip! Check it out 📸',
  'Had an amazing time at the tech conference today! So many great speakers and insights.',
  'Beautiful sunset today! Nature never fails to amaze me 🌅',
  'Working on a new project and loving every minute of it! 💻',
  'Coffee and coding - the perfect combination ☕',
  'Weekend vibes! Time to relax and recharge 🌟',
  'Excited to share my latest creation with you all! 🎨',
  'Learning something new every day keeps life interesting 📚',
];

const LOCATIONS = ['New York, NY', 'San Francisco, CA', 'London, UK', 'Tokyo, Japan', 'Paris, France'];

export const usePostManagement = () => {
  const [posts, setPosts] = useState<ExtendedPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate mock posts with improved structure
  const generateMockPosts = useCallback((count: number = 15): ExtendedPost[] => {
    return Array.from({ length: count }, (_, index) => {
      const user = MOCK_USERS[index % MOCK_USERS.length];
      const content = MOCK_CONTENTS[index % MOCK_CONTENTS.length];
      const hasImage = Math.random() > 0.3; // Increased chance of having an image
      const hasVideo = !hasImage && Math.random() > 0.7;
      const imageUrl = hasImage ? MOCK_IMAGES.POSTS[Math.floor(Math.random() * MOCK_IMAGES.POSTS.length)] : undefined;
      
      return {
        id: `post-${Date.now()}-${index}`,
        user_id: user.id,
        content,
        image_url: imageUrl,
        video_url: hasVideo ? '/api/placeholder/video/640/360' : undefined,
        created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
        profiles: {
          id: user.id,
          full_name: user.name,
          avatar_url: user.avatar,
        },
        likes_count: Math.floor(Math.random() * 100),
        comments_count: Math.floor(Math.random() * 20),
        user_has_liked: Math.random() > 0.7,
        user_has_saved: Math.random() > 0.8,
        location: Math.random() > 0.6 ? LOCATIONS[index % LOCATIONS.length] : undefined,
        type: hasVideo ? 'video' : hasImage ? 'photo' : 'text',
      };
    });
  }, []);

  // Fetch posts with error handling
  const fetchPosts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newPosts = generateMockPosts(15);
      setPosts(newPosts);
      
      toast.success('Posts loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load posts';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [generateMockPosts]);

  // Load more posts
  const loadMorePosts = useCallback(async () => {
    try {
      setIsLoadingMore(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const morePosts = generateMockPosts(10);
      setPosts(prev => [...prev, ...morePosts]);
      
      toast.success('More posts loaded!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more posts';
      toast.error(errorMessage);
    } finally {
      setIsLoadingMore(false);
    }
  }, [generateMockPosts]);

  // Refresh posts
  const refreshPosts = useCallback(async () => {
    try {
      setError(null);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const refreshedPosts = generateMockPosts(15);
      setPosts(refreshedPosts);
      
      toast.success('Posts refreshed!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh posts';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [generateMockPosts]);

  // Create new post
  const createPost = useCallback(async (content: string, image?: File) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const user = MOCK_USERS[0]; // Current user
      const newPost: ExtendedPost = {
        id: `post-${Date.now()}`,
        user_id: user.id,
        content,
        image_url: image ? URL.createObjectURL(image) : undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        profiles: {
          id: user.id,
          full_name: user.name,
          avatar_url: user.avatar,
        },
        likes_count: 0,
        comments_count: 0,
        user_has_liked: false,
        user_has_saved: false,
        type: image ? 'photo' : 'text',
      };
      
      setPosts(prev => [newPost, ...prev]);
      toast.success('Post created successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create post';
      toast.error(errorMessage);
    }
  }, []);

  return {
    posts,
    isLoading,
    isLoadingMore,
    error,
    fetchPosts,
    loadMorePosts,
    refreshPosts,
    createPost,
  };
};

export type { ExtendedPost };