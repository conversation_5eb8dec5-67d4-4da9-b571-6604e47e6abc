// Consolidated shared types to eliminate duplication across components

export interface BaseUser {
  id: string;
  name: string;
  avatar: string;
  isOnline?: boolean;
  lastActive?: Date;
  lastSeen?: Date;
  isVerified?: boolean;
}

export interface BasePost {
  id: string;
  user_id: string;
  content: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  reactions?: Record<string, number>;
  feeling?: string;
  location?: string;
  tagged_friends?: string[];
  privacy?: string;
  is_live?: boolean;
  isPoll?: boolean;
  pollOptions?: string[];
  pollVotes?: Record<string, number>;
}

export interface BaseComment {
  id: string;
  post_id?: string;
  user_id: string;
  content: string;
  created_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  user_has_liked?: boolean;
  replies?: BaseComment[];
}

export interface BaseMessage {
  id: string;
  conversationId?: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'emoji' | 'sticker' | 'location';
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  isRead?: boolean;
  edited?: boolean;
  editedAt?: Date;
  replyTo?: string;
  reactions?: {
    emoji: string;
    userId: string;
    timestamp: Date;
  }[] | { [emoji: string]: string[] };
  attachments?: {
    id: string;
    type: 'image' | 'file' | 'audio' | 'video';
    url: string;
    name: string;
    size: number;
    thumbnail?: string;
    duration?: number;
  }[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  readBy?: { [userId: string]: string };
}

export interface BaseConversation {
  id: string;
  participants: BaseUser[];
  lastMessage?: BaseMessage;
  unreadCount: number;
  isTyping?: string[];
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BaseNotification {
  id: string;
  type: 'like' | 'comment' | 'friend_request' | 'message' | 'event' | 'birthday' | 'mention' | 'share' | 'reaction';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isNew: boolean;
  actionUrl?: string;
  actor: BaseUser;
  metadata?: {
    postId?: string;
    eventId?: string;
    messageId?: string;
    reactionType?: string;
    [key: string]: unknown;
  };
}

export interface BaseSearchResult {
  id: string;
  type: 'user' | 'post' | 'page' | 'group' | 'event' | 'hashtag';
  title: string;
  description?: string;
  avatar?: string;
  thumbnail?: string;
  timestamp?: string;
  location?: string;
  relevanceScore: number;
  metadata?: Record<string, string | number | boolean>;
}

export interface BaseGroup {
  id: string;
  name: string;
  description: string;
  avatar: string;
  coverImage: string;
  privacy: 'public' | 'private' | 'secret';
  memberCount: number;
  members: BaseGroupMember[];
  posts: BasePost[];
  tags: string[];
  rules: string[];
  createdAt: Date;
  isJoined: boolean;
  userRole?: 'admin' | 'moderator' | 'member';
  pendingRequests?: number;
  events?: number;
  files?: number;
}

export interface BaseGroupMember extends BaseUser {
  role: 'admin' | 'moderator' | 'member';
  joinedAt: Date;
}

export interface BaseEvent {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  isVirtual: boolean;
  organizer: BaseUser;
  attendees: BaseUser[];
  coverImage?: string;
  privacy: 'public' | 'private';
  category: string;
  tags: string[];
}

export interface BaseStory {
  id: string;
  user: BaseUser;
  type: 'photo' | 'video' | 'text';
  media?: string;
  content: string;
  background?: string;
  timestamp: string;
  createdAt: Date;
  expiresAt: Date;
  isViewed: boolean;
  viewedBy?: string[];
  reactions?: Record<string, number>;
  privacy?: 'public' | 'friends' | 'close-friends';
}

// Common component props interfaces
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface BaseCardProps extends BaseComponentProps {
  variant?: 'default' | 'outlined' | 'elevated';
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
}

export interface BaseModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
}

export interface BaseFormProps extends BaseComponentProps {
  onSubmit: (data: Record<string, unknown>) => void;
  isLoading?: boolean;
  disabled?: boolean;
  validationSchema?: Record<string, unknown>;
}

export interface BaseListProps<T> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loading?: boolean;
  empty?: React.ReactNode;
  loadMore?: () => void;
  hasMore?: boolean;
}

export interface BaseSearchProps extends BaseComponentProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  suggestions?: string[];
  showSuggestions?: boolean;
  debounceMs?: number;
}

export interface BaseFilterProps extends BaseComponentProps {
  filters: Record<string, unknown>;
  onFilterChange: (filters: Record<string, unknown>) => void;
  onClear: () => void;
  isOpen?: boolean;
}

// Utility types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

export interface InfiniteQueryPage<T> {
  items: T[];
  nextCursor?: string;
  hasMore: boolean;
}

// Theme and UI types
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  fontFamily: string;
  borderRadius: number;
}

export interface BreakpointConfig {
  mobile: number;
  tablet: number;
  desktop: number;
  wide: number;
}

// Performance and analytics types
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  bundleSize: number;
}

export interface AnalyticsEvent {
  name: string;
  properties: Record<string, unknown>;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}

// Error handling types
export interface AppError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: Date;
  stack?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppError;
}

// Feature flags
export interface FeatureFlags {
  virtualScrolling: boolean;
  infiniteScroll: boolean;
  realTimeUpdates: boolean;
  offlineSupport: boolean;
  analytics: boolean;
  advancedSearch: boolean;
  videoCall: boolean;
  liveStreaming: boolean;
  stories: boolean;
  marketplace: boolean;
  groups: boolean;
  events: boolean;
  gaming: boolean;
  dating: boolean;
  jobs: boolean;
  weather: boolean;
  memories: boolean;
}

// Device and responsive types
export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type Orientation = 'portrait' | 'landscape';

export interface DeviceInfo {
  type: DeviceType;
  orientation: Orientation;
  screenWidth: number;
  screenHeight: number;
  userAgent: string;
  isTouch: boolean;
}

// Utility helper types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// Event handler types
export type EventHandler<T = unknown> = (event: T) => void;
export type AsyncEventHandler<T = unknown> = (event: T) => Promise<void>;

// Generic callback types
export type Callback<T = void> = () => T;
export type AsyncCallback<T = void> = () => Promise<T>;
export type CallbackWithParam<P, T = void> = (param: P) => T;
export type AsyncCallbackWithParam<P, T = void> = (param: P) => Promise<T>;
