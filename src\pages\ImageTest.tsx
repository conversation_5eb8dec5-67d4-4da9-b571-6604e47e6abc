import React from 'react';
import SimpleImage from '@/components/SimpleImage';
import { MOCK_IMAGES } from '@/lib/constants';

const ImageTestPage = () => {
  const testPosts = [
    {
      id: '1',
      content: 'Test post with image',
      image_url: MOCK_IMAGES.POSTS[0],
      user: { name: 'Test User', avatar: MOCK_IMAGES.AVATARS[0] }
    },
    {
      id: '2', 
      content: 'Another test post with image',
      image_url: MOCK_IMAGES.POSTS[1],
      user: { name: 'Test User 2', avatar: MOCK_IMAGES.AVATARS[1] }
    }
  ];

  return (
    <div className="max-w-2xl mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">News Feed Image Test</h1>
      
      {testPosts.map(post => (
        <div key={post.id} className="bg-white rounded-lg shadow-md p-4">
          {/* Post Header */}
          <div className="flex items-center space-x-3 mb-3">
            <img 
              src={post.user.avatar} 
              alt={post.user.name}
              className="w-10 h-10 rounded-full"
            />
            <div>
              <h3 className="font-semibold">{post.user.name}</h3>
              <p className="text-sm text-gray-500">Just now</p>
            </div>
          </div>
          
          {/* Post Content */}
          <p className="mb-3">{post.content}</p>
          
          {/* Post Image */}
          {post.image_url && (
            <div className="mb-3">
              <SimpleImage
                src={post.image_url}
                alt="Post image"
                className="w-full rounded-lg"
                width={600}
                height={400}
              />
            </div>
          )}
          
          {/* Debug Info */}
          <div className="text-xs text-gray-400 bg-gray-50 p-2 rounded">
            <p>Image URL: {post.image_url}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ImageTestPage;
