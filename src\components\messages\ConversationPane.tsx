import React from 'react';
import { MessageCircle, Video, Phone, Shield, ShieldOff } from 'lucide-react';
import MessageBubble from '../MessageBubble';
import MessageComposer from '../MessageComposer';
import ConversationHeader from './ConversationHeader';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MOCK_IMAGES } from '@/lib/constants';
import { toast } from 'sonner';

import { Message, Conversation } from '@/types/messaging';

interface ConversationPaneProps {
  selectedConversation: Conversation | null;
  messages: Message[];
  showConversation: boolean;
  isMobile: boolean;
  isConnected: boolean;
  onBackToList: () => void;
  onSendMessage: (content: string) => void;
  onStartVideoCall?: (conversationId: string) => void;
  onStartAudioCall?: (conversationId: string) => void;
  onMarkAsRead?: (conversationId: string, messageId: string) => void;
  onAddReaction?: (messageId: string, emoji: string) => void;
}

const ConversationPane: React.FC<ConversationPaneProps> = ({
  selectedConversation,
  messages,
  showConversation,
  isMobile,
  isConnected,
  onBackToList,
  onSendMessage,
  onStartVideoCall,
  onStartAudioCall,
  onMarkAsRead,
  onAddReaction
}) => {
  if (!showConversation || !selectedConversation) {
    // Empty state for desktop when no conversation is selected
    if (!isMobile) {
      return (
        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center p-5">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-blue-900/30">
              <MessageCircle className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 dark:text-white">Your Messages</h3>
            <p className="text-gray-500 mb-4 dark:text-gray-400">Select a conversation to start chatting</p>
            <div className="flex items-center justify-center space-x-2">
              {isConnected ? (
                <Badge variant="default" className="flex items-center space-x-1 bg-green-500 hover:bg-green-600">
                  <Shield className="w-3 h-3" />
                  <span>End-to-End Encrypted</span>
                </Badge>
              ) : (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <ShieldOff className="w-3 h-3" />
                  <span>Offline Mode</span>
                </Badge>
              )}
            </div>
          </div>
        </div>
      );
    }
    return null;
  }

  const handleVideoCall = () => {
    if (onStartVideoCall) {
      onStartVideoCall(selectedConversation.id);
    } else {
      toast.info('Video call feature available when connected');
    }
  };

  const handleAudioCall = () => {
    if (onStartAudioCall) {
      onStartAudioCall(selectedConversation.id);
    } else {
      toast.info('Audio call feature available when connected');
    }
  };

  const handleReaction = (messageId: string, emoji: string) => {
    if (onAddReaction) {
      onAddReaction(messageId, emoji);
    } else {
      toast.info('Reactions available when connected');
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden">
      {/* Enhanced Conversation Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <ConversationHeader
          user={selectedConversation.user}
          isMobile={isMobile}
          onBackToList={onBackToList}
        />
        
        {/* Call Actions and Status */}
        <div className="px-4 pb-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Badge variant="default" className="flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-xs">
                <Shield className="w-3 h-3" />
                <span>Encrypted</span>
              </Badge>
            ) : (
              <Badge variant="secondary" className="flex items-center space-x-1 text-xs">
                <ShieldOff className="w-3 h-3" />
                <span>Offline</span>
              </Badge>
            )}
          </div>
          
          {selectedConversation.type === 'direct' && (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAudioCall}
                className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
              >
                <Phone className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleVideoCall}
                className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
              >
                <Video className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-4 bg-gray-50 dark:bg-gray-900">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3 dark:bg-gray-700">
              <MessageCircle className="w-6 h-6 text-gray-400 dark:text-gray-500" />
            </div>
            <p className="text-gray-500 dark:text-gray-400">No messages yet</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Start the conversation!</p>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwn = message.senderId === 'currentUser' || message.senderId === 'current-user';
            const sender = isOwn ? {
              id: 'currentUser',
              name: 'You',
              avatar: MOCK_IMAGES.AVATARS[7],
              isOnline: true,
              lastSeen: undefined
            } : {
              id: message.senderId,
              name: selectedConversation.user?.name || selectedConversation.participants?.find(p => p.id === message.senderId)?.name || 'Unknown',
              avatar: selectedConversation.user?.avatar || selectedConversation.participants?.find(p => p.id === message.senderId)?.avatar || '',
              isOnline: selectedConversation.user?.isOnline || selectedConversation.participants?.find(p => p.id === message.senderId)?.isOnline || false,
              lastSeen: selectedConversation.user?.lastActive || undefined
            };

            return (
              <div key={message.id}>
                <MessageBubble
                  message={{
                    ...message,
                    conversationId: selectedConversation.id,
                    reactions: message.reactions || [],
                  }}
                  sender={sender}
                  isOwn={isOwn}
                  showAvatar={!isOwn || selectedConversation.type === 'group'}
                  showTimestamp={true}
                  onReply={(message) => toast.info('Reply feature coming soon')}
                  onEdit={(message) => toast.info('Edit feature coming soon')}
                  onDelete={(message) => toast.info('Delete feature coming soon')}
                  onReaction={isConnected ? handleReaction : undefined}
                />
              </div>
            );
          })
        )}
        
        {/* Typing indicators */}
        {selectedConversation.isTyping && Object.entries(selectedConversation.isTyping).some(([userId, isTyping]) => 
          userId !== 'currentUser' && userId !== 'current-user' && isTyping
        ) && (
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span>
              {selectedConversation.user?.name || 'Someone'} is typing...
            </span>
          </div>
        )}
      </div>
      
      {/* Enhanced Message Input */}
      <MessageComposer
        conversationId={selectedConversation.id}
        onSendMessage={onSendMessage}
        placeholder={isConnected ? "Type an encrypted message..." : "Type a message..."}
        isEncrypted={isConnected}
      />
    </div>
  );
};

export default ConversationPane;
