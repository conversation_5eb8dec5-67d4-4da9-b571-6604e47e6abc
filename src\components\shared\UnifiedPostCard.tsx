import React, { useState, useCallback, memo, useMemo } from 'react';
import { MessageCircle, Share, MoreHorizontal, ThumbsUp, Bookmark, Heart, Eye } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatTimeAgo } from '@/lib/utils';
import { toast } from 'sonner';
import ReactionPicker from '../ReactionPicker';
import { useSavedItems } from '@/hooks/useSavedItems';
import OptimizedImage from '@/components/OptimizedImage';
import { BasePost, BaseComment } from '@/types/shared';
import { motion, AnimatePresence } from 'framer-motion';

// Memoize formatTimeAgo to avoid unnecessary recalculations
import { memoize } from '@/lib/utils';
const memoizedFormatTimeAgo = memoize(formatTimeAgo);

interface UnifiedPostCardProps {
  post: BasePost;
  onInteraction?: (postId: string, action: string, data?: any) => void;
  isVisible?: boolean;
  priority?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  showComments?: boolean;
  maxCommentsToShow?: number;
  className?: string;
}

const UnifiedPostCard: React.FC<UnifiedPostCardProps> = memo(({
  post,
  onInteraction,
  isVisible = true,
  priority = false,
  variant = 'default',
  showActions = true,
  showComments = true,
  maxCommentsToShow = 3,
  className = ''
}) => {
  // State management
  const [showCommentsSection, setShowCommentsSection] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isLiked, setIsLiked] = useState(post?.user_has_liked || false);
  const [likesCount, setLikesCount] = useState(post?.likes_count || 0);
  const [comments, setComments] = useState<BaseComment[]>([]);
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const [currentReaction, setCurrentReaction] = useState<string | null>(null);
  const [userPollVote, setUserPollVote] = useState<number | null>(null);
  const [pollVotes, setPollVotes] = useState<Record<string, number>>(
    post?.pollVotes || {}
  );

  // Use saved items hook
  const { isItemSaved, savePostItem, removeItem } = useSavedItems();
  const isSaved = isItemSaved(post?.id || '');

  // Memoized calculations
  const timeAgo = useMemo(() => {
    return post?.created_at ? memoizedFormatTimeAgo(post.created_at) : '';
  }, [post?.created_at]);

  const totalPollVotes = useMemo(() => {
    return Object.values(pollVotes).reduce((sum, votes) => sum + votes, 0);
  }, [pollVotes]);

  // Event handlers
  const handleLike = useCallback(() => {
    const newIsLiked = !isLiked;
    const newLikesCount = newIsLiked ? likesCount + 1 : Math.max(0, likesCount - 1);
    
    setIsLiked(newIsLiked);
    setLikesCount(newLikesCount);
    
    onInteraction?.(post.id, 'like', { isLiked: newIsLiked, likesCount: newLikesCount });
    toast.success(newIsLiked ? 'Post liked' : 'Post unliked');
  }, [isLiked, likesCount, post.id, onInteraction]);

  const handleSave = useCallback(() => {
    if (!post?.id) return;
    
    if (isSaved) {
      removeItem(post.id);
      toast.success('Post removed from saved');
    } else {
      // Create rich post data for saved collection
      const postData = {
        title: `Post by ${post.profiles?.full_name || 'Anonymous'}`,
        content: post.content || '',
        image: post.image_url,
        creator: {
          name: post.profiles?.full_name || 'Anonymous',
          avatar: post.profiles?.avatar_url || '',
          verified: false
        },
        engagement: {
          likes: post.likes_count || 0,
          comments: post.comments_count || 0,
          shares: 0
        },
        originalDate: post.created_at,
        collection: 'default'
      };
      
      savePostItem(post.id, postData);
      toast.success('Post saved');
    }
    
    onInteraction?.(post.id, 'save', { isSaved: !isSaved });
  }, [isSaved, post, savePostItem, removeItem, onInteraction]);

  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: `Post by ${post.profiles?.full_name || 'Unknown'}`,
        text: post.content || '',
        url: window.location.href,
      }).catch(() => {
        navigator.clipboard.writeText(window.location.href);
        toast.success('Post link copied to clipboard');
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Post link copied to clipboard');
    }
    
    onInteraction?.(post.id, 'share');
  }, [post.profiles?.full_name, post.content, post.id, onInteraction]);

  const handleSubmitComment = useCallback(() => {
    if (!newComment.trim()) return;
    
    const newCommentObj: BaseComment = {
      id: Date.now().toString(),
      content: newComment,
      created_at: new Date().toISOString(),
      user_id: 'current-user',
      profiles: {
        id: 'current-user',
        full_name: 'You',
        avatar_url: ''
      }
    };
    
    setComments(prev => [...prev, newCommentObj]);
    setNewComment('');
    
    onInteraction?.(post.id, 'comment', { comment: newCommentObj });
    toast.success('Comment added');
  }, [newComment, post.id, onInteraction]);

  const handleReaction = useCallback((emoji: string) => {
    setCurrentReaction(emoji);
    setShowReactionPicker(false);
    
    onInteraction?.(post.id, 'reaction', { emoji });
    toast.success(`Reacted with ${emoji}`);
  }, [post.id, onInteraction]);

  const handlePollVote = useCallback((optionIndex: number) => {
    if (userPollVote === optionIndex) return;
    
    setPollVotes(prev => {
      const newVotes = { ...prev };
      if (userPollVote !== null) {
        newVotes[userPollVote] = Math.max(0, (newVotes[userPollVote] || 0) - 1);
      }
      newVotes[optionIndex] = (newVotes[optionIndex] || 0) + 1;
      return newVotes;
    });
    
    setUserPollVote(optionIndex);
    onInteraction?.(post.id, 'poll_vote', { optionIndex });
    toast.success('Vote recorded');
  }, [userPollVote, post.id, onInteraction]);

  // Render helpers
  const renderPostHeader = () => (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <Avatar className={variant === 'compact' ? 'w-8 h-8' : 'w-10 h-10'}>
          <AvatarImage src={post?.profiles?.avatar_url} />
          <AvatarFallback className="bg-blue-500 text-white">
            {post?.profiles?.full_name?.charAt(0) || 'U'}
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className={`font-semibold text-gray-900 hover:underline cursor-pointer dark:text-gray-100 ${
            variant === 'compact' ? 'text-sm' : 'text-base'
          }`}>
            {post?.profiles?.full_name || 'Anonymous User'}
          </h3>
          <div className="flex items-center space-x-2">
            <p className={`text-gray-500 dark:text-gray-400 ${
              variant === 'compact' ? 'text-xs' : 'text-sm'
            }`}>
              {timeAgo}
            </p>
            {post?.location && (
              <span className="text-gray-500 dark:text-gray-400 text-xs">
                📍 {post.location}
              </span>
            )}
            {post?.feeling && (
              <span className="text-gray-500 dark:text-gray-400 text-xs">
                😊 {post.feeling}
              </span>
            )}
          </div>
        </div>
      </div>
      
      {showActions && (
        <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      )}
    </div>
  );

  const renderPostContent = () => (
    <div className={variant === 'compact' ? 'mt-2' : 'mt-3'}>
      {post?.content && (
        <p className={`text-gray-900 dark:text-gray-100 ${
          variant === 'compact' ? 'text-sm' : 'text-base'
        } whitespace-pre-wrap`}>
          {post.content}
        </p>
      )}
      
      {post?.image_url && (
        <div className="mt-3 w-full">
          <OptimizedImage
            src={post.image_url}
            alt="Post content"
            className={`w-full h-auto object-cover rounded-md cursor-pointer hover:opacity-95 transition-opacity ${
              variant === 'compact' ? 'max-h-48' : 'max-h-96'
            }`}
            lazy={!priority}
            priority={priority}
            width={600}
            height={400}
            placeholder="skeleton"
            onLoad={() => onInteraction?.(post.id, 'image_loaded')}
          />
        </div>
      )}
      
      {post?.isPoll && post?.pollOptions && (
        <div className="mt-3 space-y-2">
          {post.pollOptions.map((option, index) => {
            const votes = pollVotes[index] || 0;
            const percentage = totalPollVotes > 0 ? (votes / totalPollVotes) * 100 : 0;
            const isSelected = userPollVote === index;
            
            return (
              <div
                key={index}
                className={`relative p-3 rounded-lg border cursor-pointer transition-colors ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
                onClick={() => handlePollVote(index)}
              >
                <div className="flex items-center justify-between">
                  <span className={`font-medium ${
                    variant === 'compact' ? 'text-sm' : 'text-base'
                  }`}>
                    {option}
                  </span>
                  <span className={`text-gray-500 ${
                    variant === 'compact' ? 'text-xs' : 'text-sm'
                  }`}>
                    {percentage.toFixed(1)}%
                  </span>
                </div>
                <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <p className={`text-gray-500 mt-1 ${
                  variant === 'compact' ? 'text-xs' : 'text-sm'
                }`}>
                  {votes} {votes === 1 ? 'vote' : 'votes'}
                </p>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );

  const renderPostActions = () => {
    if (!showActions) return null;
    
    return (
      <div className={`flex items-center justify-between ${
        variant === 'compact' ? 'mt-2' : 'mt-4'
      } pt-3 border-t border-gray-100 dark:border-gray-700`}>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={`flex items-center space-x-1 ${
                isLiked ? 'text-blue-500' : 'text-gray-500 hover:text-blue-500'
              }`}
            >
              <ThumbsUp className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
              <span className={variant === 'compact' ? 'text-xs' : 'text-sm'}>
                {likesCount}
              </span>
            </Button>
            
            {showReactionPicker && (
              <div className="absolute bottom-full left-0 mb-2 z-10">
                <ReactionPicker onSelect={handleReaction} />
              </div>
            )}
          </div>
          
          {showComments && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCommentsSection(!showCommentsSection)}
              className="flex items-center space-x-1 text-gray-500 hover:text-blue-500"
            >
              <MessageCircle className="w-4 h-4" />
              <span className={variant === 'compact' ? 'text-xs' : 'text-sm'}>
                {post?.comments_count || comments.length}
              </span>
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShare}
            className="flex items-center space-x-1 text-gray-500 hover:text-blue-500"
          >
            <Share className="w-4 h-4" />
            <span className={variant === 'compact' ? 'text-xs' : 'text-sm'}>
              Share
            </span>
          </Button>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSave}
          className={`${
            isSaved ? 'text-yellow-500' : 'text-gray-500 hover:text-yellow-500'
          }`}
        >
          <Bookmark className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
        </Button>
      </div>
    );
  };

  const renderComments = () => {
    if (!showComments || !showCommentsSection) return null;
    
    return (
      <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
        <AnimatePresence>
          {comments.slice(0, maxCommentsToShow).map((comment) => (
            <motion.div
              key={comment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="flex items-start space-x-3 mb-3"
            >
              <Avatar className="w-8 h-8">
                <AvatarImage src={comment.profiles?.avatar_url} />
                <AvatarFallback>
                  {comment.profiles?.full_name?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2">
                  <p className="font-medium text-sm text-gray-900 dark:text-gray-100">
                    {comment.profiles?.full_name || 'Anonymous'}
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {comment.content}
                  </p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {memoizedFormatTimeAgo(comment.created_at)}
                </p>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        <div className="flex items-center space-x-2 mt-3">
          <Avatar className="w-8 h-8">
            <AvatarFallback>Y</AvatarFallback>
          </Avatar>
          <div className="flex-1 flex items-center space-x-2">
            <Input
              placeholder="Write a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSubmitComment()}
              className="flex-1 text-sm"
            />
            <Button
              onClick={handleSubmitComment}
              size="sm"
              disabled={!newComment.trim()}
              className="px-3 h-9"
            >
              Post
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (!isVisible || !post) return null;

  return (
    <Card className={`shadow-sm hover:shadow-md transition-shadow bg-white border-0 shadow-gray-100 mb-4 dark:bg-gray-800 dark:shadow-gray-900 overflow-hidden ${className}`}>
      <CardContent className={variant === 'compact' ? 'p-3' : 'p-4'}>
        {renderPostHeader()}
        {renderPostContent()}
        {renderPostActions()}
        {renderComments()}
      </CardContent>
    </Card>
  );
}, (prevProps, nextProps) => {
  // Optimized comparison for better performance
  if (!prevProps.post || !nextProps.post) return false;
  
  return (
    prevProps.post.id === nextProps.post.id &&
    prevProps.post.likes_count === nextProps.post.likes_count &&
    prevProps.post.comments_count === nextProps.post.comments_count &&
    prevProps.post.user_has_liked === nextProps.post.user_has_liked &&
    prevProps.variant === nextProps.variant &&
    prevProps.showActions === nextProps.showActions &&
    prevProps.showComments === nextProps.showComments
  );
});

UnifiedPostCard.displayName = 'UnifiedPostCard';

export default UnifiedPostCard;
