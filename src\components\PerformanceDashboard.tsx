import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Cpu, 
  Database, 
  Globe, 
  Image, 
  Zap, 
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import PerformanceService, { PerformanceMetrics } from '@/services/PerformanceService';

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
}

const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [historicalData, setHistoricalData] = useState<PerformanceMetrics[]>([]);
  
  const performanceService = useMemo(() => PerformanceService.getInstance(), []);

  useEffect(() => {
    const updateMetrics = (newMetrics: PerformanceMetrics) => {
      setMetrics(newMetrics);
      setHistoricalData(prev => [...prev.slice(-29), newMetrics]); // Keep last 30 entries
      checkForAlerts(newMetrics);
    };

    performanceService.on('metrics-updated', updateMetrics);
    
    // Initial metrics load
    const initialMetrics = performanceService.getMetrics();
    updateMetrics(initialMetrics);

    return () => {
      performanceService.off('metrics-updated', updateMetrics);
    };
  }, [performanceService]);

  const checkForAlerts = (currentMetrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];

    // FPS alerts
    if (currentMetrics.fps < 30) {
      newAlerts.push({
        id: `fps-${Date.now()}`,
        type: currentMetrics.fps < 15 ? 'error' : 'warning',
        message: `Low FPS detected: ${currentMetrics.fps}`,
        timestamp: new Date(),
        metric: 'fps',
        value: currentMetrics.fps,
        threshold: 30
      });
    }

    // Memory alerts
    if (currentMetrics.memory > 100) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: currentMetrics.memory > 200 ? 'error' : 'warning',
        message: `High memory usage: ${currentMetrics.memory}MB`,
        timestamp: new Date(),
        metric: 'memory',
        value: currentMetrics.memory,
        threshold: 100
      });
    }

    // Render time alerts
    if (currentMetrics.renderTime > 16) {
      newAlerts.push({
        id: `render-${Date.now()}`,
        type: currentMetrics.renderTime > 33 ? 'error' : 'warning',
        message: `Slow render time: ${currentMetrics.renderTime.toFixed(2)}ms`,
        timestamp: new Date(),
        metric: 'renderTime',
        value: currentMetrics.renderTime,
        threshold: 16
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev.slice(0, 9)]); // Keep last 10 alerts
    }
  };

  const getMetricStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.warning) return 'warning';
    return 'error';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 dark:text-green-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'error': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const clearCache = () => {
    performanceService.clearCache();
    setAlerts(prev => [{
      id: `cache-cleared-${Date.now()}`,
      type: 'info',
      message: 'Cache cleared successfully',
      timestamp: new Date(),
      metric: 'cacheHitRate',
      value: 0,
      threshold: 0
    }, ...prev.slice(0, 9)]);
  };

  const optimizePerformance = () => {
    performanceService.cleanupMemory();
    if ('gc' in window) {
      (window as unknown as { gc: () => void }).gc();
    }
    setAlerts(prev => [{
      id: `optimized-${Date.now()}`,
      type: 'info',
      message: 'Performance optimization completed',
      timestamp: new Date(),
      metric: 'memory',
      value: metrics?.memory || 0,
      threshold: 0
    }, ...prev.slice(0, 9)]);
  };

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-20 z-50"
      >
        <Activity className="w-4 h-4 mr-2" />
        Performance
      </Button>
    );
  }

  if (!metrics) {
    return (
      <Card className="fixed bottom-4 right-20 w-80 z-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center justify-between">
            Performance Monitor
            <Button variant="ghost" size="sm" onClick={() => setIsVisible(false)}>
              ×
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const fpsStatus = getMetricStatus(metrics.fps, { good: 50, warning: 30 });
  const memoryStatus = getMetricStatus(metrics.memory, { good: 50, warning: 100 });
  const renderStatus = getMetricStatus(metrics.renderTime, { good: 16, warning: 33 });

  return (
    <Card className="fixed bottom-4 right-20 w-96 max-h-96 overflow-y-auto z-50 bg-white dark:bg-gray-900 border shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center">
            <Activity className="w-4 h-4 mr-2" />
            Performance Monitor
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" onClick={optimizePerformance}>
              <Zap className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm" onClick={clearCache}>
              <Database className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsVisible(false)}>
              ×
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Core Metrics */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className={`text-xs font-medium flex items-center ${getStatusColor(fpsStatus)}`}>
                {getStatusIcon(fpsStatus)}
                <span className="ml-1">FPS</span>
              </span>
              <span className="text-xs font-mono">{metrics.fps}</span>
            </div>
            <Progress value={Math.min(metrics.fps, 60) / 60 * 100} className="h-1" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className={`text-xs font-medium flex items-center ${getStatusColor(memoryStatus)}`}>
                {getStatusIcon(memoryStatus)}
                <Cpu className="w-3 h-3 ml-1" />
                <span className="ml-1">RAM</span>
              </span>
              <span className="text-xs font-mono">{metrics.memory}MB</span>
            </div>
            <Progress value={Math.min(metrics.memory, 200) / 200 * 100} className="h-1" />
          </div>
        </div>

        {/* Secondary Metrics */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className={`text-xs font-medium flex items-center ${getStatusColor(renderStatus)}`}>
                {getStatusIcon(renderStatus)}
                <span className="ml-1">Render</span>
              </span>
              <span className="text-xs font-mono">{metrics.renderTime.toFixed(1)}ms</span>
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium flex items-center">
                <Globe className="w-3 h-3" />
                <span className="ml-1">Network</span>
              </span>
              <span className="text-xs font-mono">{metrics.networkLatency}ms</span>
            </div>
          </div>
        </div>

        {/* Cache & Bundle Info */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium flex items-center">
                <Database className="w-3 h-3" />
                <span className="ml-1">Cache</span>
              </span>
              <span className="text-xs font-mono">{metrics.cacheHitRate.toFixed(1)}%</span>
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium flex items-center">
                <Image className="w-3 h-3" />
                <span className="ml-1">Images</span>
              </span>
              <span className="text-xs font-mono">{metrics.imageLoadTime.toFixed(0)}ms</span>
            </div>
          </div>
        </div>

        {/* Performance Trend */}
        {historicalData.length > 5 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium flex items-center">
                <TrendingUp className="w-3 h-3" />
                <span className="ml-1">Trend (FPS)</span>
              </span>
            </div>
            <div className="flex items-end space-x-1 h-8">
              {historicalData.slice(-10).map((data, index) => (
                <div
                  key={index}
                  className="bg-blue-500 rounded-sm w-2"
                  style={{
                    height: `${Math.max((data.fps / 60) * 100, 5)}%`,
                    opacity: 0.3 + (index / 10) * 0.7
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Alerts */}
        {alerts.length > 0 && (
          <div className="space-y-2">
            <span className="text-xs font-medium flex items-center">
              <AlertTriangle className="w-3 h-3" />
              <span className="ml-1">Recent Alerts</span>
            </span>
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {alerts.slice(0, 3).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between">
                  <Badge 
                    variant={alert.type === 'error' ? 'destructive' : alert.type === 'warning' ? 'secondary' : 'default'}
                    className="text-xs"
                  >
                    {alert.message}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {alert.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex justify-between pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={clearCache}
            className="text-xs"
          >
            Clear Cache
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={optimizePerformance}
            className="text-xs"
          >
            Optimize
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformanceDashboard;
