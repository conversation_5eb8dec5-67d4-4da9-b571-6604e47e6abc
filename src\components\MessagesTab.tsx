import React from 'react';
import { OptimizedMessaging } from './messaging';

const MessagesTab = () => {
  // Using a current user ID that exists in our mock data
  const currentUserId = "current_user"; // This will be the logged-in user
  
  return (
    <div className="h-screen">
      <OptimizedMessaging
        currentUserId={currentUserId}
        onClose={() => {
          console.log('Messaging closed');
        }}
      />
    </div>
  );
};

export default MessagesTab;