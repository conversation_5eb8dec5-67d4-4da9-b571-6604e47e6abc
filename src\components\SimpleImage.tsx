import React, { useState } from 'react';
import { ImageOff } from 'lucide-react';

interface SimpleImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
}

const SimpleImage: React.FC<SimpleImageProps> = ({ 
  src, 
  alt, 
  className = '', 
  width = 600, 
  height = 400 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  if (!src) {
    return null;
  }

  if (hasError) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${className}`}>
        <div className="text-center text-gray-400">
          <ImageOff className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">Image failed to load</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && (
        <div className="absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700" />
      )}
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => {
          setIsLoaded(true);
        }}
        onError={(e) => {
          setHasError(true);
        }}
        style={{ width: `${width}px`, height: `${height}px` }}
      />
    </div>
  );
};

export default SimpleImage;
