import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { Conversation, Message } from '@/types/messaging';
import { messagingService } from '@/services/MessagingService';

interface UseMessagingReturn {
  conversations: Conversation[];
  messages: Record<string, Message[]>;
  selectedConversationId: string | null;
  searchQuery: string;
  isMobile: boolean;
  showConversation: boolean;
  isConnected: boolean;
  isConnecting: boolean;
  setSearchQuery: (query: string) => void;
  handleSelectConversation: (id: string) => void;
  handleSendMessage: (content: string) => void;
  handleBackToList: () => void;
  setShowConversation: (show: boolean) => void;
  startVideoCall: (conversationId: string) => void;
  startAudioCall: (conversationId: string) => void;
  markAsRead: (conversationId: string, messageId: string) => void;
  addReaction: (messageId: string, emoji: string) => void;
}

export const useMessaging = (): UseMessagingReturn => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Record<string, Message[]>>({});
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showConversation, setShowConversation] = useState(window.innerWidth >= 768);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(true);
  const initialized = useRef(false);

  // Convert MessagingService types to local types
  const convertConversation = useCallback((serviceConv: any): Conversation => ({
    id: serviceConv.id,
    type: serviceConv.type,
    name: serviceConv.name,
    user: serviceConv.type === 'direct' ? {
      id: serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.id || '',
      name: serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.name || 'Unknown',
      avatar: serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.avatar || '',
      isOnline: serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.isOnline || false,
      lastActive: serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.isOnline ? 'Active now' : 
        (serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.lastSeen ? 
          formatLastSeen(serviceConv.participants.find((p: any) => p.id !== messagingService.getCurrentUserId())?.lastSeen) : 'Offline')
    } : undefined,
    participants: serviceConv.participants,
    lastMessage: serviceConv.lastMessage ? {
      content: serviceConv.lastMessage.content,
      timestamp: formatTimestamp(serviceConv.lastMessage.timestamp),
      isRead: serviceConv.lastMessage.isRead
    } : {
      content: 'No messages yet',
      timestamp: 'now',
      isRead: true
    },
    unreadCount: serviceConv.unreadCount || 0,
    isTyping: serviceConv.isTyping,
    settings: serviceConv.settings
  }), []);

  const convertMessage = useCallback((serviceMsg: any): Message => ({
    id: serviceMsg.id,
    conversationId: serviceMsg.conversationId,
    content: serviceMsg.content,
    timestamp: new Date(serviceMsg.timestamp),
    senderId: serviceMsg.senderId,
    type: serviceMsg.type,
    status: serviceMsg.status,
    reactions: serviceMsg.reactions,
    attachments: serviceMsg.attachments,
    replyTo: serviceMsg.replyTo,
    isEdited: serviceMsg.isEdited,
    editedAt: serviceMsg.editedAt,
    isDeleted: serviceMsg.isDeleted,
    deletedAt: serviceMsg.deletedAt
  }), []);

  const formatLastSeen = useCallback((lastSeen: Date): string => {
    const now = new Date();
    const diff = now.getTime() - lastSeen.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 5) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  }, []);

  const formatTimestamp = useCallback((timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  }, []);

  // Initialize messaging service
  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;

    const loadInitialData = () => {
      try {
        // Load conversations
        const serviceConversations = messagingService.getConversations();
        const convertedConversations = serviceConversations.map(convertConversation);
        setConversations(convertedConversations);

        // Load messages for each conversation
        const allMessages: Record<string, Message[]> = {};
        serviceConversations.forEach(conv => {
          const serviceMessages = messagingService.getMessages(conv.id);
          allMessages[conv.id] = serviceMessages.map(convertMessage);
        });
        setMessages(allMessages);

        // Select first conversation on desktop
        if (!isMobile && serviceConversations.length > 0) {
          setSelectedConversationId(serviceConversations[0].id);
          setShowConversation(true);
        }

        setIsConnecting(false);
      } catch (error) {
        console.error('Failed to load initial data:', error);
        toast.error('Failed to load conversations');
        setIsConnecting(false);
      }
    };

    // Set up event listeners
    const handleMessageSent = (message: any) => {
      const convertedMessage = convertMessage(message);
      setMessages(prev => ({
        ...prev,
        [message.conversationId]: [...(prev[message.conversationId] || []), convertedMessage]
      }));
      
      // Update conversation last message
      updateConversationLastMessage(message.conversationId, message);
    };

    const handleMessageReceived = (message: any) => {
      const convertedMessage = convertMessage(message);
      setMessages(prev => ({
        ...prev,
        [message.conversationId]: [...(prev[message.conversationId] || []), convertedMessage]
      }));
      
      // Update conversation last message and unread count
      updateConversationLastMessage(message.conversationId, message, true);
      
      // Show notification if not in current conversation
      if (selectedConversationId !== message.conversationId) {
        const conversation = messagingService.getConversation(message.conversationId);
        const sender = conversation?.participants.find(p => p.id === message.senderId);
        toast.info(`New message from ${sender?.name || 'Unknown'}`);
      }
    };

    const handleMessageStatusUpdated = (message: any) => {
      setMessages(prev => {
        const updated = { ...prev };
        if (updated[message.conversationId]) {
          const messageIndex = updated[message.conversationId].findIndex(m => m.id === message.id);
          if (messageIndex !== -1) {
            updated[message.conversationId][messageIndex] = convertMessage(message);
          }
        }
        return updated;
      });
    };

    const handleTypingUpdated = (typing: any) => {
      setConversations(prev => prev.map(conv => 
        conv.id === typing.conversationId 
          ? { ...conv, isTyping: { ...conv.isTyping, [typing.userId]: typing.isTyping } }
          : conv
      ));
    };

    const handleConversationUpdated = (conversation: any) => {
      const convertedConversation = convertConversation(conversation);
      setConversations(prev => prev.map(conv => 
        conv.id === conversation.id ? convertedConversation : conv
      ));
    };

    const handleConnected = () => {
      setIsConnected(true);
      setIsConnecting(false);
      toast.success('Connected to messaging service');
    };

    const handleDisconnected = () => {
      setIsConnected(false);
      toast.error('Disconnected from messaging service');
    };

    const handleError = (error: any) => {
      console.error('Messaging service error:', error);
      toast.error('Messaging service error');
    };

    // Register event listeners
    messagingService.on('messageSent', handleMessageSent);
    messagingService.on('messageReceived', handleMessageReceived);
    messagingService.on('messageStatusUpdated', handleMessageStatusUpdated);
    messagingService.on('typingUpdated', handleTypingUpdated);
    messagingService.on('conversationUpdated', handleConversationUpdated);
    messagingService.on('connected', handleConnected);
    messagingService.on('disconnected', handleDisconnected);
    messagingService.on('error', handleError);

    // Load initial data
    loadInitialData();

    // Try to connect to server
    messagingService.connectToServer().catch(() => {
      // If connection fails, we'll work with mock data
      setIsConnected(false);
      setIsConnecting(false);
    });

    // Cleanup
    return () => {
      messagingService.off('messageSent', handleMessageSent);
      messagingService.off('messageReceived', handleMessageReceived);
      messagingService.off('messageStatusUpdated', handleMessageStatusUpdated);
      messagingService.off('typingUpdated', handleTypingUpdated);
      messagingService.off('conversationUpdated', handleConversationUpdated);
      messagingService.off('connected', handleConnected);
      messagingService.off('disconnected', handleDisconnected);
      messagingService.off('error', handleError);
    };
  }, [convertConversation, convertMessage, formatTimestamp, isMobile, selectedConversationId]);

  const updateConversationLastMessage = useCallback((conversationId: string, message: any, incrementUnread = false) => {
    setConversations(prev => prev.map(conv => 
      conv.id === conversationId 
        ? {
            ...conv,
            lastMessage: {
              content: message.content,
              timestamp: formatTimestamp(new Date(message.timestamp)),
              isRead: message.senderId === messagingService.getCurrentUserId()
            },
            unreadCount: incrementUnread && selectedConversationId !== conversationId 
              ? (conv.unreadCount || 0) + 1 
              : selectedConversationId === conversationId ? 0 : conv.unreadCount
          }
        : conv
    ));
  }, [formatTimestamp, selectedConversationId]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile && !selectedConversationId && conversations.length > 0) {
        setSelectedConversationId(conversations[0].id);
        setShowConversation(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [selectedConversationId, conversations]);

  const handleSelectConversation = useCallback((id: string) => {
    setSelectedConversationId(id);
    setShowConversation(true);
    
    // Mark conversation as read
    const conversation = conversations.find(c => c.id === id);
    if (conversation && conversation.unreadCount > 0) {
      const latestMessage = messages[id]?.[messages[id].length - 1];
      if (latestMessage) {
        messagingService.markAsRead(id, latestMessage.id);
      }
    }
  }, [conversations, messages]);

  const handleSendMessage = useCallback(async (content: string) => {
    if (!selectedConversationId || !content.trim()) return;
    
    try {
      if (isConnected) {
        // Use encrypted messaging if connected
        await messagingService.sendEncryptedMessage(selectedConversationId, content);
      } else {
        // Fallback to regular messaging
        messagingService.sendMessage(selectedConversationId, content);
      }
      
      // Send typing indicator (stop typing)
      messagingService.sendTypingIndicator(selectedConversationId, false);
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  }, [selectedConversationId, isConnected]);

  const handleBackToList = useCallback(() => {
    setShowConversation(false);
  }, []);

  const startVideoCall = useCallback(async (conversationId: string) => {
    try {
      await messagingService.initiateCall(conversationId, 'video');
      toast.success('Video call started');
    } catch (error) {
      console.error('Failed to start video call:', error);
      toast.error('Failed to start video call');
    }
  }, []);

  const startAudioCall = useCallback(async (conversationId: string) => {
    try {
      await messagingService.initiateCall(conversationId, 'audio');
      toast.success('Audio call started');
    } catch (error) {
      console.error('Failed to start audio call:', error);
      toast.error('Failed to start audio call');
    }
  }, []);

  const markAsRead = useCallback((conversationId: string, messageId: string) => {
    messagingService.markAsRead(conversationId, messageId);
  }, []);

  const addReaction = useCallback((messageId: string, emoji: string) => {
    messagingService.addReaction(messageId, emoji);
  }, []);

  return {
    conversations,
    messages,
    selectedConversationId,
    searchQuery,
    isMobile,
    showConversation,
    isConnected,
    isConnecting,
    setSearchQuery,
    handleSelectConversation,
    handleSendMessage,
    handleBackToList,
    setShowConversation,
    startVideoCall,
    startAudioCall,
    markAsRead,
    addReaction
  };
};