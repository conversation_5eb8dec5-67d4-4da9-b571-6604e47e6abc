import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bookmark, Grid, List, Filter, Search, Layers, Clock, Tag, Heart, MessageCircle, Share, X, MapPin, ThumbsUp, Phone, Mail, MoreHorizontal } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import OptimizedImage from '@/components/OptimizedImage';
import { MOCK_IMAGES, STORAGE_KEYS } from '@/lib/constants';
import { storage } from '@/lib/storage';
import { formatTimeAgo } from '@/lib/utils';
import { toast } from 'sonner';
import { useSavedItems, SavedItem } from '@/hooks/useSavedItems';

const Saved = () => {
  const navigate = useNavigate();
  const { items, isLoading, removeItem } = useSavedItems();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedCollection, setSelectedCollection] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('recent');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [collections, setCollections] = useState<string[]>(['All Saved', 'Articles', 'Videos', 'Posts', 'Marketplace', 'Events', 'Photography', 'Tech Tips']);
  
  // New state for detail modals
  const [selectedItem, setSelectedItem] = useState<SavedItem | null>(null);
  const [showItemDetail, setShowItemDetail] = useState(false);

  // Extract unique collections from items
  useEffect(() => {
    if (items.length > 0) {
      const uniqueCollections = ['All Saved', ...new Set(items.map(item => item.collection))];
      setCollections(uniqueCollections);
    }
  }, [items]);

  // Load view mode preference
  useEffect(() => {
    const savedViewMode = storage.get<'grid' | 'list'>(STORAGE_KEYS.SAVED_VIEW_MODE);
    if (savedViewMode) {
      setViewMode(savedViewMode);
    }
  }, []);

  // Save view mode preference
  useEffect(() => {
    storage.set(STORAGE_KEYS.SAVED_VIEW_MODE, viewMode);
  }, [viewMode]);

  const handleRemoveItem = (itemId: string) => {
    removeItem(itemId);
  };

  const handleToggleFavorite = (itemId: string) => {
    // Update storage directly since we don't have this in the hook yet
    const savedItems = storage.get<SavedItem[]>(STORAGE_KEYS.SAVED_ITEMS, []);
    const updatedItems = savedItems.map(item => 
      item.id === itemId ? { ...item, isFavorite: !item.isFavorite } : item
    );
    storage.set(STORAGE_KEYS.SAVED_ITEMS, updatedItems);
    
    // Trigger a reload of the saved items
    window.dispatchEvent(new StorageEvent('storage', {
      key: STORAGE_KEYS.SAVED_ITEMS,
      newValue: JSON.stringify(updatedItems)
    }));
    
    const item = items.find(i => i.id === itemId);
    toast.success(item?.isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const handleCreateCollection = () => {
    const newCollection = prompt('Enter collection name:');
    if (newCollection && !collections.includes(newCollection)) {
      setCollections([...collections, newCollection]);
      toast.success(`Collection "${newCollection}" created`);
    }
  };



  const handleShare = (itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      navigator.clipboard.writeText(`Check out this ${item.type}: ${item.title}`);
      toast.success('Link copied to clipboard');
    }
  };

  const handleItemClick = (item: SavedItem) => {
    // Open detail modal for all item types to show full content
    setSelectedItem(item);
    setShowItemDetail(true);
  };

  // Filter items based on search, type, and collection
  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (item.content?.toLowerCase().includes(searchQuery.toLowerCase()) || false);
    const matchesType = selectedType === 'all' || item.type === selectedType;
    const matchesCollection = selectedCollection === 'all' || selectedCollection === 'All Saved' || 
                             item.collection === selectedCollection;
    return matchesSearch && matchesType && matchesCollection;
  });

  // Sort items
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.savedDate).getTime() - new Date(a.savedDate).getTime();
      case 'oldest':
        return new Date(a.savedDate).getTime() - new Date(b.savedDate).getTime();
      case 'title':
        return a.title.localeCompare(b.title);
      case 'type':
        return a.type.localeCompare(b.type);
      default:
        return 0;
    }
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'post': return <Layers className="w-4 h-4 text-blue-500" />;
      case 'video': return <Play className="w-4 h-4 text-red-500" />;
      case 'article': return <FileText className="w-4 h-4 text-green-500" />;
      case 'event': return <Calendar className="w-4 h-4 text-purple-500" />;
      case 'marketplace': return <Tag className="w-4 h-4 text-orange-500" />;
      case 'photo': return <Image className="w-4 h-4 text-pink-500" />;
      case 'link': return <Link className="w-4 h-4 text-indigo-500" />;
      default: return <Bookmark className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'post': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'video': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'article': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'event': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'marketplace': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'photo': return 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300';
      case 'link': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <div className="w-full">
      <div className="container-responsive mx-auto py-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Saved Items</h1>
              <p className="text-gray-600 dark:text-gray-300">Organize and revisit content you've saved</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="dark:border-gray-600"
              >
                {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
              </Button>
              <Button
                variant={showFilters ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="dark:border-gray-600"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
              <Button onClick={handleCreateCollection}>
                <Bookmark className="w-4 h-4 mr-2" />
                New Collection
              </Button>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search saved items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px] dark:bg-gray-700 dark:border-gray-600">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="title">Title (A-Z)</SelectItem>
                <SelectItem value="type">Content Type</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filters */}
          {showFilters && (
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1 dark:text-gray-200">Content Type</label>
                    <Select value={selectedType} onValueChange={setSelectedType}>
                      <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="post">Posts</SelectItem>
                        <SelectItem value="video">Videos</SelectItem>
                        <SelectItem value="article">Articles</SelectItem>
                        <SelectItem value="event">Events</SelectItem>
                        <SelectItem value="marketplace">Marketplace</SelectItem>
                        <SelectItem value="photo">Photos</SelectItem>
                        <SelectItem value="link">Links</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 dark:text-gray-200">Collection</label>
                    <Select value={selectedCollection} onValueChange={setSelectedCollection}>
                      <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600">
                        <SelectValue placeholder="Select collection" />
                      </SelectTrigger>
                      <SelectContent>
                        {collections.map((collection) => (
                          <SelectItem key={collection} value={collection}>
                            {collection}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button 
                      variant="outline" 
                      className="w-full dark:border-gray-600 dark:text-gray-200"
                      onClick={() => {
                        setSearchQuery('');
                        setSelectedType('all');
                        setSelectedCollection('all');
                        setSortBy('recent');
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="all">All Saved</TabsTrigger>
              <TabsTrigger value="favorites">Favorites</TabsTrigger>
              <TabsTrigger value="collections">Collections</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {isLoading ? (
                // Loading skeleton
                <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Card key={index} className="overflow-hidden animate-pulse">
                      <div className="h-48 bg-gray-200 dark:bg-gray-700"></div>
                      <CardContent className="p-4">
                        <div className="h-6 w-3/4 bg-gray-200 rounded mb-2 dark:bg-gray-700"></div>
                        <div className="h-4 w-1/2 bg-gray-200 rounded mb-3 dark:bg-gray-700"></div>
                        <div className="h-4 w-full bg-gray-200 rounded mb-4 dark:bg-gray-700"></div>
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex justify-between">
                            <div className="h-8 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
                            <div className="h-8 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : sortedItems.length > 0 ? (
                <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                  {sortedItems.map((item) => (
                    <Card 
                      key={item.id} 
                      className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer bg-white dark:bg-gray-800 border-0 shadow-sm"
                      onClick={() => handleItemClick(item)}
                    >
                      {/* Header - Author info */}
                      <div className="p-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {item.creator && (
                            <>
                              <Avatar className="w-10 h-10">
                                <AvatarImage src={item.creator.avatar} />
                                <AvatarFallback className="bg-blue-500 text-white">{item.creator.name?.charAt(0) || 'U'}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="flex items-center space-x-1">
                                  <h3 className="font-semibold text-gray-900 dark:text-white hover:underline">{item.creator.name}</h3>
                                  {item.creator.verified && (
                                    <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                                      <span className="text-white text-xs">✓</span>
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    {item.originalDate ? formatTimeAgo(item.originalDate) : formatTimeAgo(item.savedDate)}
                                  </p>
                                  <Badge className={getTypeColor(item.type)}>
                                    <span className="capitalize text-xs">{item.type}</span>
                                  </Badge>
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {item.isFavorite && (
                            <Heart className="w-4 h-4 fill-red-500 text-red-500" />
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Add more options menu here
                            }}
                            className="hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                          </Button>
                        </div>
                      </div>

                      {/* Content Text */}
                      <div className="px-4 pb-3">
                        {item.title && (
                          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {item.title}
                          </h2>
                        )}
                        {item.content && (
                          <p className="text-gray-900 dark:text-white whitespace-pre-wrap leading-relaxed">
                            {item.content}
                          </p>
                        )}
                      </div>

                      {/* Image */}
                      {item.image && (
                        <div className="w-full relative">
                          <OptimizedImage
                            src={item.image}
                            alt={item.title || 'Saved content'}
                            className="w-full h-auto max-h-96 object-cover cursor-pointer hover:opacity-95 transition-opacity"
                          />
                        </div>
                      )}

                      {/* Engagement Stats */}
                      {item.engagement && (
                        <div className="px-4 py-3 flex items-center justify-between text-sm text-gray-500 border-t border-gray-100 dark:border-gray-700 dark:text-gray-400">
                          <div className="flex items-center space-x-1">
                            {item.engagement.likes > 0 && (
                              <>
                                <div className="flex -space-x-1">
                                  <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                                    <ThumbsUp className="w-3 h-3 text-white" />
                                  </div>
                                </div>
                                <span className="hover:underline cursor-pointer">
                                  {item.engagement.likes} {item.engagement.likes === 1 ? 'reaction' : 'reactions'}
                                </span>
                              </>
                            )}
                          </div>
                          <div className="flex space-x-4">
                            <span className="hover:underline cursor-pointer">
                              {item.engagement.comments} {item.engagement.comments === 1 ? 'comment' : 'comments'}
                            </span>
                            <span>{item.engagement.shares} shares</span>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="px-4 py-2 flex items-center justify-between">
                        <div className="flex items-center space-x-1 flex-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              toast.success('Liked!');
                            }}
                          >
                            <ThumbsUp className="w-4 h-4 mr-2" />
                            <span className="font-medium">Like</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              toast.success('Opening comments...');
                            }}
                          >
                            <MessageCircle className="w-4 h-4 mr-2" />
                            <span className="font-medium">Comment</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleShare(item.id);
                            }}
                          >
                            <Share className="w-4 h-4 mr-2" />
                            <span className="font-medium">Share</span>
                          </Button>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleFavorite(item.id);
                            }}
                            className={`${item.isFavorite ? 'text-red-500' : 'text-gray-600'} hover:bg-gray-100 dark:hover:bg-gray-700`}
                          >
                            <Heart className={`w-4 h-4 ${item.isFavorite ? 'fill-current' : ''}`} />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveItem(item.id);
                            }}
                            className="text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Saved Info Footer */}
                      <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-700 pt-3">
                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="flex items-center space-x-1 dark:border-gray-600">
                              <Bookmark className="w-3 h-3" />
                              <span>Saved {formatTimeAgo(item.savedDate)}</span>
                            </Badge>
                            <Badge variant="outline" className="dark:border-gray-600">
                              {item.collection}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-white rounded-lg shadow-sm dark:bg-gray-800">
                  <Bookmark className="w-16 h-16 text-gray-400 mx-auto mb-4 dark:text-gray-600" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 dark:text-white">No saved items</h3>
                  <p className="text-gray-500 mb-6 dark:text-gray-400">
                    {searchQuery 
                      ? `No results for "${searchQuery}"`
                      : selectedType !== 'all'
                        ? `No saved ${selectedType}s found`
                        : selectedCollection !== 'all' && selectedCollection !== 'All Saved'
                          ? `No items in "${selectedCollection}" collection`
                          : "You haven't saved any items yet"}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    {(searchQuery || selectedType !== 'all' || (selectedCollection !== 'all' && selectedCollection !== 'All Saved')) && (
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setSearchQuery('');
                          setSelectedType('all');
                          setSelectedCollection('all');
                        }}
                        className="dark:border-gray-600 dark:text-gray-200"
                      >
                        Clear Filters
                      </Button>
                    )}
                    <Button onClick={() => window.history.back()}>
                      Back to Feed
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="favorites" className="space-y-6">
              {isLoading ? (
                // Loading skeleton (same as above)
                <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="overflow-hidden animate-pulse">
                      <div className="h-48 bg-gray-200 dark:bg-gray-700"></div>
                      <CardContent className="p-4">
                        <div className="h-6 w-3/4 bg-gray-200 rounded mb-2 dark:bg-gray-700"></div>
                        <div className="h-4 w-1/2 bg-gray-200 rounded mb-3 dark:bg-gray-700"></div>
                        <div className="h-4 w-full bg-gray-200 rounded mb-4 dark:bg-gray-700"></div>
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex justify-between">
                            <div className="h-8 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
                            <div className="h-8 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <>
                  {sortedItems.filter(item => item.isFavorite).length > 0 ? (
                    <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                      {sortedItems.filter(item => item.isFavorite).map((item) => (
                        <Card 
                          key={item.id} 
                          className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer bg-white dark:bg-gray-800"
                          onClick={() => handleItemClick(item)}
                        >
                          {/* Header - Author info */}
                          <div className="p-4 flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              {item.creator && (
                                <>
                                  <Avatar className="w-10 h-10">
                                    <AvatarImage src={item.creator.avatar} />
                                    <AvatarFallback>{item.creator.name?.charAt(0) || 'U'}</AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <div className="flex items-center space-x-1">
                                      <h3 className="font-semibold text-gray-900 dark:text-white">{item.creator.name}</h3>
                                      {item.creator.verified && (
                                        <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                                          <span className="text-white text-xs">✓</span>
                                        </div>
                                      )}
                                    </div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      {item.originalDate ? formatTimeAgo(item.originalDate) : formatTimeAgo(item.savedDate)}
                                    </p>
                                  </div>
                                </>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge className={getTypeColor(item.type)}>
                                <span className="capitalize text-xs">{item.type}</span>
                              </Badge>
                              <Heart className="w-4 h-4 fill-red-500 text-red-500" />
                            </div>
                          </div>

                          {/* Content Text */}
                          <div className="px-4 pb-3">
                            {item.title && (
                              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                {item.title}
                              </h2>
                            )}
                            {item.content && (
                              <p className="text-gray-900 dark:text-white whitespace-pre-wrap leading-relaxed">
                                {item.content}
                              </p>
                            )}
                          </div>

                          {/* Image */}
                          {item.image && (
                            <div className="w-full relative">
                              <OptimizedImage
                                src={item.image}
                                alt={item.title || 'Saved content'}
                                className="w-full h-auto max-h-96 object-cover cursor-pointer hover:opacity-95 transition-opacity"
                              />
                            </div>
                          )}

                          {/* Engagement Stats */}
                          {item.engagement && (
                            <div className="px-4 py-3 flex items-center justify-between text-sm text-gray-500 border-b border-gray-100 dark:border-gray-700 dark:text-gray-400">
                              <div className="flex items-center space-x-4">
                                {item.engagement.likes > 0 && (
                                  <div className="flex items-center space-x-1">
                                    <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                                      <ThumbsUp className="w-3 h-3 text-white" />
                                    </div>
                                    <span>{item.engagement.likes}</span>
                                  </div>
                                )}
                              </div>
                              <div className="flex space-x-4">
                                <span>{item.engagement.comments} {item.engagement.comments === 1 ? 'comment' : 'comments'}</span>
                                <span>{item.engagement.shares} shares</span>
                              </div>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="px-4 py-2 flex items-center justify-between">
                            <div className="flex items-center space-x-1 flex-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toast.success('Liked!');
                                }}
                              >
                                <ThumbsUp className="w-4 h-4 mr-2" />
                                <span className="font-medium">Like</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toast.success('Opening comments...');
                                }}
                              >
                                <MessageCircle className="w-4 h-4 mr-2" />
                                <span className="font-medium">Comment</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex-1 text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 justify-center"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleShare(item.id);
                                }}
                              >
                                <Share className="w-4 h-4 mr-2" />
                                <span className="font-medium">Share</span>
                              </Button>
                            </div>
                            
                            <div className="flex items-center space-x-2 ml-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleFavorite(item.id);
                                }}
                                className="text-red-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Heart className="w-4 h-4 fill-current" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveItem(item.id);
                                }}
                                className="text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Saved Info Footer */}
                          <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-700 pt-3">
                            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="flex items-center space-x-1 dark:border-gray-600">
                                  <Bookmark className="w-3 h-3" />
                                  <span>Saved {formatTimeAgo(item.savedDate)}</span>
                                </Badge>
                                <Badge variant="outline" className="dark:border-gray-600">
                                  {item.collection}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 bg-white rounded-lg shadow-sm dark:bg-gray-800">
                      <Heart className="w-16 h-16 text-gray-400 mx-auto mb-4 dark:text-gray-600" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-2 dark:text-white">No favorite items</h3>
                      <p className="text-gray-500 mb-6 dark:text-gray-400">
                        Mark items as favorites to find them here
                      </p>
                      <Button onClick={() => setActiveTab('all')}>
                        View All Saved Items
                      </Button>
                    </div>
                  )}
                </>
              )}
            </TabsContent>

            <TabsContent value="collections" className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {collections.filter(c => c !== 'All Saved').map((collection) => {
                  const collectionItems = items.filter(item => item.collection === collection);
                  const itemCount = collectionItems.length;
                  const coverImage = collectionItems[0]?.image || MOCK_IMAGES.POSTS[0];
                  
                  return (
                    <Card key={collection} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="relative h-40">
                        <img
                          src={coverImage}
                          alt={collection}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                        <div className="absolute bottom-4 left-4 right-4">
                          <h3 className="text-white text-lg font-semibold">{collection}</h3>
                          <p className="text-white text-sm">{itemCount} {itemCount === 1 ? 'item' : 'items'}</p>
                        </div>
                      </div>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div className="flex space-x-2">
                            {collectionItems.slice(0, 3).map((item, index) => (
                              <div key={index} className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                                {item.image ? (
                                  <img src={item.image} alt="" className="w-full h-full object-cover" />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center">
                                    {getTypeIcon(item.type)}
                                  </div>
                                )}
                              </div>
                            ))}
                            {itemCount > 3 && (
                              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium dark:bg-gray-700 dark:text-white">
                                +{itemCount - 3}
                              </div>
                            )}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedCollection(collection);
                              setActiveTab('all');
                            }}
                            className="dark:border-gray-600 dark:text-gray-200"
                          >
                            View
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
                
                {/* Create new collection card */}
                <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer h-full" onClick={handleCreateCollection}>
                  <div className="h-40 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <Bookmark className="w-16 h-16 text-white" />
                  </div>
                  <CardContent className="p-4 text-center">
                    <h3 className="font-semibold text-lg mb-2 dark:text-white">Create New Collection</h3>
                    <p className="text-gray-600 text-sm dark:text-gray-300">Organize your saved items into custom collections</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Item Detail Modal */}
      {selectedItem && (
        <Dialog open={showItemDetail} onOpenChange={setShowItemDetail}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>{selectedItem.title}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowItemDetail(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </DialogTitle>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Content Side */}
              <div className="space-y-4">
                {/* Creator Info */}
                {selectedItem.creator && (
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={selectedItem.creator.avatar} />
                      <AvatarFallback>{selectedItem.creator.name?.charAt(0) || 'U'}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-lg">{selectedItem.creator.name}</h3>
                        {selectedItem.creator.verified && (
                          <Badge variant="secondary" className="text-xs">
                            <span className="text-blue-600">✓</span> Verified
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {selectedItem.originalDate ? formatTimeAgo(selectedItem.originalDate) : 'Posted recently'}
                      </p>
                    </div>
                  </div>
                )}

                {/* Content */}
                <div>
                  <h2 className="text-xl font-bold mb-3">{selectedItem.title}</h2>
                  {selectedItem.content && (
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap mb-4">
                      {selectedItem.content}
                    </p>
                  )}
                </div>

                {/* Tags and Info */}
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getTypeColor(selectedItem.type)}>
                      <span className="capitalize">{selectedItem.type}</span>
                    </Badge>
                    <Badge variant="outline">
                      {selectedItem.collection}
                    </Badge>
                    {selectedItem.isFavorite && (
                      <Badge variant="secondary" className="bg-red-50 text-red-600 dark:bg-red-900/30">
                        <Heart className="w-3 h-3 mr-1 fill-current" />
                        Favorite
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Saved</span>
                      <p className="font-medium">{formatTimeAgo(selectedItem.savedDate)}</p>
                    </div>
                    {selectedItem.originalDate && (
                      <div>
                        <span className="text-gray-500">Originally Posted</span>
                        <p className="font-medium">{formatTimeAgo(selectedItem.originalDate)}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Engagement Stats */}
                {selectedItem.engagement && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-3">Engagement</h4>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{selectedItem.engagement.likes}</div>
                        <div className="text-sm text-gray-500">Likes</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{selectedItem.engagement.comments}</div>
                        <div className="text-sm text-gray-500">Comments</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-purple-600">{selectedItem.engagement.shares}</div>
                        <div className="text-sm text-gray-500">Shares</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Image/Visual Side */}
              <div className="space-y-4">
                {selectedItem.image && (
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                    <OptimizedImage
                      src={selectedItem.image}
                      alt={selectedItem.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  {selectedItem.type === 'post' && (
                    <Button 
                      className="w-full" 
                      size="lg"
                      onClick={() => {
                        setShowItemDetail(false);
                        navigate('/');
                        toast.success('Navigated to feed to find original post');
                      }}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      View Original Post
                    </Button>
                  )}

                  {selectedItem.type === 'marketplace' && (
                    <Button 
                      className="w-full" 
                      size="lg"
                      onClick={() => {
                        setShowItemDetail(false);
                        navigate('/marketplace');
                        toast.success('Navigated to marketplace');
                      }}
                    >
                      <Tag className="w-4 h-4 mr-2" />
                      View in Marketplace
                    </Button>
                  )}

                  {selectedItem.type === 'event' && (
                    <Button 
                      className="w-full" 
                      size="lg"
                      onClick={() => {
                        setShowItemDetail(false);
                        navigate('/events');
                        toast.success('Navigated to events');
                      }}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      View Event Details
                    </Button>
                  )}

                  {selectedItem.type === 'video' && (
                    <Button 
                      className="w-full" 
                      size="lg"
                      onClick={() => {
                        setShowItemDetail(false);
                        navigate('/watch');
                        toast.success('Navigated to video player');
                      }}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Watch Video
                    </Button>
                  )}

                  {selectedItem.url && (
                    <Button 
                      variant="outline"
                      className="w-full" 
                      size="lg"
                      onClick={() => {
                        window.open(selectedItem.url, '_blank');
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open Original Link
                    </Button>
                  )}
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline"
                      onClick={() => {
                        handleToggleFavorite(selectedItem.id);
                      }}
                    >
                      <Heart className={`w-4 h-4 mr-2 ${selectedItem.isFavorite ? 'fill-current text-red-500' : ''}`} />
                      {selectedItem.isFavorite ? 'Favorited' : 'Favorite'}
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => {
                        handleShare(selectedItem.id);
                      }}
                    >
                      <Share className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                  </div>

                  <Button 
                    variant="destructive"
                    className="w-full"
                    onClick={() => {
                      handleRemoveItem(selectedItem.id);
                      setShowItemDetail(false);
                    }}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Remove from Saved
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Icon components
const Play = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <polygon points="5 3 19 12 5 21 5 3" />
  </svg>
);

const Calendar = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
    <line x1="16" y1="2" x2="16" y2="6" />
    <line x1="8" y1="2" x2="8" y2="6" />
    <line x1="3" y1="10" x2="21" y2="10" />
  </svg>
);

const FileText = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
    <polyline points="14 2 14 8 20 8" />
    <line x1="16" y1="13" x2="8" y2="13" />
    <line x1="16" y1="17" x2="8" y2="17" />
    <line x1="10" y1="9" x2="8" y2="9" />
  </svg>
);

const Image = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
    <circle cx="8.5" cy="8.5" r="1.5" />
    <polyline points="21 15 16 10 5 21" />
  </svg>
);

const Link = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
  </svg>
);

const ExternalLink = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M15 3h6v6" />
    <path d="M10 14 21 3" />
    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
  </svg>
);

export default Saved;