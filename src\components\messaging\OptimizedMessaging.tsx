import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Phone,
  Video,
  Info,
  Smile,
  Paperclip,
  Send,
  Mic,
  MoreHorizontal,
  Settings,
  Users,
  Check,
  CheckCheck,
  Reply,
  Heart,
  ThumbsUp,
  ArrowLeft,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';
import { formatDistanceToNow } from 'date-fns';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import { Message, Conversation } from '@/types/enhanced-messaging';
import OptimizedMessageSearch from './OptimizedMessageSearch';
import CallInterface from './CallInterface';
import MessagingSettings from '../MessagingSettings';

interface OptimizedMessagingProps {
  currentUserId: string;
  onClose?: () => void;
}

const OptimizedMessaging: React.FC<OptimizedMessagingProps> = ({
  currentUserId,
  onClose
}) => {
  const {
    conversations,
    activeConversation,
    messages,
    isConnected,
    typingUsers,
    currentCall,
    selectConversation,
    sendMessage,
    addReaction,
    searchMessages,
    clearSearch,
    startCall,
    endCall,
    setTyping,
  } = useEnhancedMessaging(currentUserId);

  // Mobile responsive state
  const [isMobile, setIsMobile] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // Message state
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setShowSidebar(true);
      }
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Online users
  const onlineUsers = useMemo(() => 
    conversations
      .filter(c => c.user?.isOnline)
      .slice(0, 8)
  , [conversations]);

  // Send message handler
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || !activeConversation) return;

    try {
      await sendMessage(newMessage, {
        replyTo: replyingTo?.id
      });
      
      setNewMessage('');
      setReplyingTo(null);
      setTyping(false);
      
      // Close sidebar on mobile after sending
      if (isMobile) {
        setShowSidebar(false);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [newMessage, activeConversation, replyingTo, sendMessage, setTyping, isMobile]);

  // Handle conversation select
  const handleSelectConversation = useCallback((conversationId: string) => {
    selectConversation(conversationId);
    if (isMobile) {
      setShowSidebar(false);
    }
  }, [selectConversation, isMobile]);

  // Message status
  const getMessageStatus = useCallback((message: Message) => {
    if (message.status === 'sent') {
      return <Check className="w-3 h-3 text-gray-400" />;
    } else if (message.status === 'delivered') {
      return <CheckCheck className="w-3 h-3 text-gray-400" />;
    } else if (message.status === 'read') {
      return <CheckCheck className="w-3 h-3 text-blue-500" />;
    }
    return null;
  }, []);

  // Format message time
  const formatMessageTime = useCallback((timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(new Date(timestamp));
  }, []);

  // Render message
  const renderMessage = useCallback((message: Message) => {
    const isOwnMessage = message.senderId === currentUserId;
    
    // Find the original message being replied to
    const originalMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;
    
    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-3`}
      >
        <div className={`max-w-[70%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
          {originalMessage && (
            <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-t-lg text-xs border-l-2 border-blue-500">
              <span className="text-gray-600 dark:text-gray-400">
                Replying to: {originalMessage.content?.slice(0, 50)}...
              </span>
            </div>
          )}
          
          <div className={`p-3 rounded-lg ${
            isOwnMessage 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
          }`}>
            <p className="text-sm break-words">{message.content || ''}</p>
            
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex gap-1 mt-2">
                {message.reactions.map((reaction, index) => (
                  <span key={index} className="text-xs bg-white bg-opacity-20 rounded-full px-2 py-1">
                    {reaction.emoji}
                  </span>
                ))}
              </div>
            )}
          </div>
          
          <div className={`flex items-center gap-1 mt-1 text-xs text-gray-500 ${
            isOwnMessage ? 'justify-end' : 'justify-start'
          }`}>
            <span>{formatMessageTime(message.timestamp)}</span>
            {isOwnMessage && getMessageStatus(message)}
          </div>
        </div>
        
        {!isOwnMessage && (
          <Avatar className="w-6 h-6 order-1 mr-2">
            <AvatarImage src={activeConversation?.user?.avatar} />
            <AvatarFallback className="text-xs">
              {activeConversation?.user?.name?.[0]}
            </AvatarFallback>
          </Avatar>
        )}
      </motion.div>
    );
  }, [currentUserId, messages, activeConversation, formatMessageTime, getMessageStatus]);

  // Handle call interface
  if (currentCall) {
    return (
      <CallInterface
        call={currentCall}
        currentUserId={currentUserId}
        onEndCall={endCall}
      />
    );
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-900 relative">
      {/* Mobile Overlay */}
      {isMobile && showSidebar && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Sidebar */}
      <motion.div 
        className={`${
          isMobile 
            ? 'fixed left-0 top-0 h-full w-80 z-50 transform transition-transform duration-300' 
            : 'w-80 relative'
        } ${
          isMobile && !showSidebar ? '-translate-x-full' : 'translate-x-0'
        } border-r border-gray-200 dark:border-gray-700 flex flex-col bg-white dark:bg-gray-900`}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-semibold">Messages</h1>
            <div className="flex gap-2">
              {isMobile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSidebar(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearch(true)}
              >
                <Search className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(true)}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Connection Status */}
          <div className="flex items-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-gray-600 dark:text-gray-400">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>

        {/* Online Users */}
        {onlineUsers.length > 0 && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Online Now ({onlineUsers.length})
            </h3>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {onlineUsers.map((conversation) => (
                <button
                  key={conversation.id}
                  onClick={() => handleSelectConversation(conversation.id)}
                  className="flex-shrink-0 flex flex-col items-center gap-1 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                >
                  <div className="relative">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={conversation.user?.avatar} />
                      <AvatarFallback>{conversation.user?.name?.[0]}</AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900" />
                  </div>
                  <span className="text-xs text-center truncate max-w-16">
                    {conversation.user?.name?.split(' ')[0]}
                  </span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Conversations List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {conversations.map((conversation) => (
              <button
                key={conversation.id}
                onClick={() => handleSelectConversation(conversation.id)}
                className={`w-full p-3 flex items-center gap-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors mb-1 ${
                  activeConversation?.id === conversation.id
                    ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                    : ''
                }`}
              >
                <Avatar>
                  <AvatarImage src={conversation.user?.avatar} />
                  <AvatarFallback>
                    {conversation.user?.name?.[0] || conversation.name?.[0]}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 text-left min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium truncate">
                      {conversation.user?.name || conversation.name}
                    </h3>
                    {conversation.lastMessage && (
                      <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
                        {formatDistanceToNow(new Date(conversation.lastMessage.timestamp), { addSuffix: true })}
                      </span>
                    )}
                  </div>
                  
                  {conversation.lastMessage && (
                    <p className="text-sm text-gray-500 truncate">
                      {conversation.lastMessage?.content || 'No messages yet'}
                    </p>
                  )}
                </div>
                
                {conversation.unreadCount && conversation.unreadCount > 0 && (
                  <Badge variant="default" className="ml-2 bg-blue-500 text-white">
                    {conversation.unreadCount}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </ScrollArea>
      </motion.div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowSidebar(true)}
                    >
                      <Menu className="w-4 h-4" />
                    </Button>
                  )}
                  <Avatar>
                    <AvatarImage src={activeConversation.user?.avatar} />
                    <AvatarFallback>
                      {activeConversation.user?.name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h2 className="font-semibold">
                      {activeConversation.user?.name || activeConversation.name}
                    </h2>
                    <p className="text-sm text-gray-500">
                      {activeConversation.user?.isOnline ? 'Online' : 'Offline'}
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => startCall('audio')}
                  >
                    <Phone className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => startCall('video')}
                  >
                    <Video className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Info className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-1">
                <AnimatePresence>
                  {messages.filter(msg => msg && msg.content && msg.senderId).map(renderMessage)}
                </AnimatePresence>
                
                {/* Typing indicator */}
                {typingUsers.length > 0 && (
                  <div className="flex items-center gap-2 text-gray-500 p-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span className="text-sm">Someone is typing...</span>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Reply Preview */}
            {replyingTo && (
              <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Reply className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Replying to: {replyingTo.content?.slice(0, 50)}...
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyingTo(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <Textarea
                    ref={textareaRef}
                    value={newMessage}
                    onChange={(e) => {
                      setNewMessage(e.target.value);
                      setTyping(e.target.value.length > 0);
                    }}
                    placeholder="Type a message..."
                    className="min-h-[40px] max-h-[120px] resize-none"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                </div>
                
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <Paperclip className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Smile className="w-4 h-4" />
                  </Button>
                  <Button 
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    size="sm"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Empty State */
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center max-w-md">
              <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-10 h-10 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Welcome to Enhanced Messaging! 👋
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {isMobile ? 'Tap the menu to select' : 'Select'} a conversation to start chatting
              </p>
              
              {isMobile && (
                <Button onClick={() => setShowSidebar(true)} className="mb-4">
                  <Menu className="w-4 h-4 mr-2" />
                  Open Conversations
                </Button>
              )}

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {conversations.length}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Conversations
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {onlineUsers.length}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Online Now
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <OptimizedMessageSearch
        isOpen={showSearch}
        onClose={() => setShowSearch(false)}
        currentUserId={currentUserId}
      />
      
      <MessagingSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        currentUserId={currentUserId}
      />
    </div>
  );
};

export default OptimizedMessaging;
