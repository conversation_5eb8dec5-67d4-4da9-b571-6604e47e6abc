import { EventEmitter } from '../utils/EventEmitter';

export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  comments: number;
  shares: number;
  uploadDate: Date;
  category: string;
  tags: string[];
  creator: {
    id: string;
    name: string;
    avatar: string;
    verified: boolean;
    subscribers: number;
  };
  isLive: boolean;
  isWatchParty: boolean;
  watchPartyId?: string;
  quality: '360p' | '480p' | '720p' | '1080p' | '4K';
  subtitles?: {
    language: string;
    url: string;
  }[];
  chapters?: {
    title: string;
    startTime: number;
    thumbnail: string;
  }[];
  analytics?: {
    watchTime: number;
    engagement: number;
    retention: number[];
  };
}

export interface WatchHistory {
  videoId: string;
  watchTime: number;
  totalDuration: number;
  timestamp: Date;
  completed: boolean;
  liked: boolean;
  shared: boolean;
  commented: boolean;
}

export interface UserPreferences {
  categories: string[];
  creators: string[];
  languages: string[];
  qualityPreference: string;
  autoplay: boolean;
  subtitles: boolean;
  notifications: boolean;
}

class VideoRecommendationService extends EventEmitter {
  private static instance: VideoRecommendationService;
  private watchHistory: Map<string, WatchHistory> = new Map();
  private userPreferences: UserPreferences;
  private currentUserId = 'current-user';
  private videos: Map<string, Video> = new Map();
  private recommendations: Video[] = [];

  static getInstance(): VideoRecommendationService {
    if (!VideoRecommendationService.instance) {
      VideoRecommendationService.instance = new VideoRecommendationService();
    }
    return VideoRecommendationService.instance;
  }

  constructor() {
    super();
    
    // Default preferences
    this.userPreferences = {
      categories: ['entertainment', 'technology', 'gaming'],
      creators: [],
      languages: ['en'],
      qualityPreference: '720p',
      autoplay: true,
      subtitles: false,
      notifications: true
    };

    this.loadUserPreferences();
    this.loadWatchHistory();
    this.initializeMockData();
  }

  private loadUserPreferences() {
    const saved = localStorage.getItem('video-preferences');
    if (saved) {
      this.userPreferences = { ...this.userPreferences, ...JSON.parse(saved) };
    }
  }

  private saveUserPreferences() {
    localStorage.setItem('video-preferences', JSON.stringify(this.userPreferences));
  }

  private loadWatchHistory() {
    const saved = localStorage.getItem('watch-history');
    if (saved) {
      const history: WatchHistory[] = JSON.parse(saved);
      history.forEach(item => {
        item.timestamp = new Date(item.timestamp);
        this.watchHistory.set(item.videoId, item);
      });
    }
  }

  private saveWatchHistory() {
    const history = Array.from(this.watchHistory.values());
    localStorage.setItem('watch-history', JSON.stringify(history));
  }

  private initializeMockData() {
    const mockVideos: Video[] = [
      {
        id: 'video-1',
        title: 'Amazing Nature Documentary',
        description: 'Explore the wonders of nature in this breathtaking documentary.',
        thumbnail: 'https://picsum.photos/400/225?random=1',
        url: 'https://example.com/video1.mp4',
        duration: 3600, // 1 hour
        views: 1250000,
        likes: 45000,
        dislikes: 1200,
        comments: 8500,
        shares: 2300,
        uploadDate: new Date('2024-01-15'),
        category: 'documentary',
        tags: ['nature', 'wildlife', 'documentary'],
        creator: {
          id: 'creator-1',
          name: 'Nature Explorer',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=nature',
          verified: true,
          subscribers: 2500000
        },
        isLive: false,
        isWatchParty: false,
        quality: '1080p'
      },
      {
        id: 'video-2',
        title: 'Tech Review: Latest Smartphone',
        description: 'In-depth review of the newest smartphone technology.',
        thumbnail: 'https://picsum.photos/400/225?random=2',
        url: 'https://example.com/video2.mp4',
        duration: 1200, // 20 minutes
        views: 850000,
        likes: 32000,
        dislikes: 800,
        comments: 5200,
        shares: 1800,
        uploadDate: new Date('2024-01-20'),
        category: 'technology',
        tags: ['tech', 'smartphone', 'review'],
        creator: {
          id: 'creator-2',
          name: 'Tech Guru',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=tech',
          verified: true,
          subscribers: 1800000
        },
        isLive: false,
        isWatchParty: true,
        watchPartyId: 'party-1',
        quality: '720p'
      },
      {
        id: 'video-3',
        title: 'Live Gaming Stream',
        description: 'Join us for an epic gaming session!',
        thumbnail: 'https://picsum.photos/400/225?random=3',
        url: 'https://example.com/live-stream',
        duration: 0, // Live stream
        views: 15000,
        likes: 2500,
        dislikes: 50,
        comments: 1200,
        shares: 300,
        uploadDate: new Date(),
        category: 'gaming',
        tags: ['gaming', 'live', 'stream'],
        creator: {
          id: 'creator-3',
          name: 'Pro Gamer',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=gamer',
          verified: false,
          subscribers: 450000
        },
        isLive: true,
        isWatchParty: false,
        quality: '720p'
      }
    ];

    mockVideos.forEach(video => {
      this.videos.set(video.id, video);
    });

    this.generateRecommendations();
  }

  // Get video by ID
  getVideo(videoId: string): Video | undefined {
    return this.videos.get(videoId);
  }

  // Get all videos
  getAllVideos(): Video[] {
    return Array.from(this.videos.values());
  }

  // Get videos by category
  getVideosByCategory(category: string): Video[] {
    return Array.from(this.videos.values()).filter(video => video.category === category);
  }

  // Get live videos
  getLiveVideos(): Video[] {
    return Array.from(this.videos.values()).filter(video => video.isLive);
  }

  // Get watch party videos
  getWatchPartyVideos(): Video[] {
    return Array.from(this.videos.values()).filter(video => video.isWatchParty);
  }

  // Get trending videos
  getTrendingVideos(): Video[] {
    return Array.from(this.videos.values())
      .sort((a, b) => {
        const aScore = a.views + a.likes * 10 + a.shares * 20;
        const bScore = b.views + b.likes * 10 + b.shares * 20;
        return bScore - aScore;
      })
      .slice(0, 20);
  }

  // Get personalized recommendations
  getRecommendations(): Video[] {
    return this.recommendations;
  }

  // Generate recommendations based on user preferences and watch history
  private generateRecommendations() {
    const allVideos = Array.from(this.videos.values());
    const watchedVideoIds = new Set(this.watchHistory.keys());
    
    // Filter out already watched videos
    const unwatchedVideos = allVideos.filter(video => !watchedVideoIds.has(video.id));
    
    // Score videos based on user preferences
    const scoredVideos = unwatchedVideos.map(video => {
      let score = 0;
      
      // Category preference
      if (this.userPreferences.categories.includes(video.category)) {
        score += 50;
      }
      
      // Creator preference
      if (this.userPreferences.creators.includes(video.creator.id)) {
        score += 100;
      }
      
      // Popularity score
      score += Math.log(video.views + 1) * 0.1;
      score += Math.log(video.likes + 1) * 0.5;
      score += video.creator.verified ? 10 : 0;
      
      // Recency score
      const daysSinceUpload = (Date.now() - video.uploadDate.getTime()) / (1000 * 60 * 60 * 24);
      score += Math.max(0, 30 - daysSinceUpload);
      
      return { video, score };
    });
    
    // Sort by score and take top recommendations
    this.recommendations = scoredVideos
      .sort((a, b) => b.score - a.score)
      .slice(0, 50)
      .map(item => item.video);
    
    this.emit('recommendations_updated', this.recommendations);
  }

  // Record watch activity
  recordWatch(videoId: string, watchTime: number, totalDuration: number) {
    const existing = this.watchHistory.get(videoId);
    const watchHistory: WatchHistory = {
      videoId,
      watchTime: Math.max(existing?.watchTime || 0, watchTime),
      totalDuration,
      timestamp: new Date(),
      completed: watchTime >= totalDuration * 0.9,
      liked: existing?.liked || false,
      shared: existing?.shared || false,
      commented: existing?.commented || false
    };
    
    this.watchHistory.set(videoId, watchHistory);
    this.saveWatchHistory();
    
    // Update recommendations based on new watch data
    this.generateRecommendations();
    
    this.emit('watch_recorded', watchHistory);
  }

  // Record interaction
  recordInteraction(videoId: string, type: 'like' | 'share' | 'comment', value: boolean = true) {
    const existing = this.watchHistory.get(videoId);
    if (existing) {
      switch (type) {
        case 'like':
          existing.liked = value;
          break;
        case 'share':
          existing.shared = value;
          break;
        case 'comment':
          existing.commented = value;
          break;
      }
      this.watchHistory.set(videoId, existing);
      this.saveWatchHistory();
    }
    
    this.emit('interaction_recorded', { videoId, type, value });
  }

  // Update user preferences
  updatePreferences(preferences: Partial<UserPreferences>) {
    this.userPreferences = { ...this.userPreferences, ...preferences };
    this.saveUserPreferences();
    this.generateRecommendations();
    this.emit('preferences_updated', this.userPreferences);
  }

  // Get user preferences
  getPreferences(): UserPreferences {
    return { ...this.userPreferences };
  }

  // Get watch history
  getWatchHistory(): WatchHistory[] {
    return Array.from(this.watchHistory.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Clear watch history
  clearWatchHistory() {
    this.watchHistory.clear();
    localStorage.removeItem('watch-history');
    this.generateRecommendations();
    this.emit('watch_history_cleared');
  }

  // Search videos
  searchVideos(query: string): Video[] {
    const searchTerms = query.toLowerCase().split(' ');
    
    return Array.from(this.videos.values()).filter(video => {
      const searchableText = [
        video.title,
        video.description,
        video.creator.name,
        ...video.tags
      ].join(' ').toLowerCase();
      
      return searchTerms.every(term => searchableText.includes(term));
    });
  }

  // Get video analytics
  getVideoAnalytics(videoId: string) {
    const video = this.videos.get(videoId);
    const watchData = this.watchHistory.get(videoId);
    
    if (!video) return null;
    
    return {
      video,
      watchData,
      recommendations: this.getRelatedVideos(videoId),
      engagement: {
        likeRatio: video.likes / (video.likes + video.dislikes),
        commentRatio: video.comments / video.views,
        shareRatio: video.shares / video.views
      }
    };
  }

  // Get related videos
  getRelatedVideos(videoId: string): Video[] {
    const video = this.videos.get(videoId);
    if (!video) return [];
    
    return Array.from(this.videos.values())
      .filter(v => v.id !== videoId)
      .filter(v => 
        v.category === video.category ||
        v.creator.id === video.creator.id ||
        v.tags.some(tag => video.tags.includes(tag))
      )
      .sort((a, b) => b.views - a.views)
      .slice(0, 10);
  }

  // Cleanup
  destroy() {
    this.removeAllListeners();
  }
}

export default VideoRecommendationService;
