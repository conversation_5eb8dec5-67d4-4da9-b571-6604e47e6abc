import React from 'react';

const SimpleImageTest = () => {
  const testImages = [
    'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop',
    'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?w=800&h=600&fit=crop',
    'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face'
  ];

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Simple Image Test</h2>
      {testImages.map((src, index) => (
        <div key={index} className="border p-4">
          <p className="mb-2">Image {index + 1}: {src}</p>
          <img 
            src={src}
            alt={`Test image ${index + 1}`}
            className="w-64 h-48 object-cover border"
            onLoad={() => console.log(`✅ Image ${index + 1} loaded successfully`)}
            onError={(e) => console.error(`❌ Image ${index + 1} failed to load:`, e)}
          />
        </div>
      ))}
    </div>
  );
};

export default SimpleImageTest;
