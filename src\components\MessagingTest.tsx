import React, { useState } from 'react';
import { OptimizedMessaging } from '@/components/messaging';

const MessagingTest = () => {
  const [currentUserId] = useState("current_user");
  
  return (
    <div className="w-full h-screen bg-gray-100">
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Messaging System Test</h1>
        <p className="text-gray-600 mb-6">
          Testing the optimized messaging system with mobile responsiveness and all features.
        </p>
      </div>
      
      <OptimizedMessaging
        currentUserId={currentUserId}
        onClose={() => console.log('Messaging closed')}
      />
    </div>
  );
};

export default MessagingTest;
