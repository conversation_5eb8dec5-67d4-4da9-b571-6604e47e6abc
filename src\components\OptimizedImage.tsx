import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';
import { optimizeImageUrl, PerformanceMonitor } from '@/utils/performanceOptimizer';
// Skeleton component replacement
const Skeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={`animate-pulse bg-gray-200 dark:bg-gray-700 ${className}`} />
);
import { ImageOff } from 'lucide-react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  className?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'skeleton' | 'none';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  sizes?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  lazy?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  quality = 80,
  className = '',
  priority = false,
  placeholder = 'skeleton',
  blurDataURL,
  onLoad,
  onError,
  sizes,
  objectFit = 'cover',
  lazy = true
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  
  // Intersection observer for lazy loading
  const { ref: inViewRef, inView } = useInView({
    threshold: 0,
    rootMargin: '50px',
    triggerOnce: true,
    skip: !lazy || priority
  });

  // Combine refs
  const setRefs = useCallback((node: HTMLImageElement | null) => {
    imgRef.current = node;
    inViewRef(node);
  }, [inViewRef]);

  // Generate optimized image URL
  const optimizedSrc = React.useMemo(() => {
    if (!src) return '';
    return optimizeImageUrl(src, width, height, quality);
  }, [src, width, height, quality]);

  // Generate responsive srcSet
  const srcSet = React.useMemo(() => {
    if (!src || !width) return undefined;
    
    const breakpoints = [0.5, 1, 1.5, 2];
    return breakpoints
      .map(multiplier => {
        const scaledWidth = Math.round(width * multiplier);
        const scaledHeight = height ? Math.round(height * multiplier) : undefined;
        const url = optimizeImageUrl(src, scaledWidth, scaledHeight, quality);
        return `${url} ${scaledWidth}w`;
      })
      .join(', ');
  }, [src, width, height, quality]);

  // Load image when in view or priority
  useEffect(() => {
    if (!src) return;
    
    const shouldLoad = priority || inView || !lazy;
    console.log('OptimizedImage: Should load?', shouldLoad, { src, priority, inView, lazy });
    
    if (!shouldLoad) return;

    console.log('OptimizedImage: Starting load for:', optimizedSrc);
    PerformanceMonitor.startMeasure(`image-load-${src}`);
    
    const img = new Image();
    
    img.onload = () => {
      console.log('OptimizedImage: ✅ Loaded successfully:', optimizedSrc);
      PerformanceMonitor.endMeasure(`image-load-${src}`);
      setCurrentSrc(optimizedSrc);
      setIsLoaded(true);
      setIsError(false);
      onLoad?.();
    };
    
    img.onerror = (error) => {
      console.error('OptimizedImage: ❌ Failed to load:', optimizedSrc, error);
      PerformanceMonitor.endMeasure(`image-load-${src}`);
      setIsError(true);
      setIsLoaded(false);
      onError?.();
    };
    
    // Set srcset if available
    if (srcSet) {
      img.srcset = srcSet;
      img.sizes = sizes || `${width}px`;
    }
    
    img.src = optimizedSrc;
    
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [optimizedSrc, srcSet, sizes, inView, priority, lazy, src, onLoad, onError, width]);

  // Preload critical images
  useEffect(() => {
    if (priority && src) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = optimizedSrc;
      if (srcSet) {
        link.setAttribute('imagesrcset', srcSet);
        link.setAttribute('imagesizes', sizes || `${width}px`);
      }
      document.head.appendChild(link);
      
      return () => {
        try {
          document.head.removeChild(link);
        } catch (e) {
          // Link might already be removed
        }
      };
    }
  }, [priority, src, optimizedSrc, srcSet, sizes, width]);

  // Render placeholder
  const renderPlaceholder = () => {
    if (placeholder === 'none') return null;
    
    if (placeholder === 'blur' && blurDataURL) {
      return (
        <img
          src={blurDataURL}
          alt=""
          className={`absolute inset-0 w-full h-full object-${objectFit} transition-opacity duration-300 ${
            isLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          style={{ filter: 'blur(10px)' }}
        />
      );
    }
    
    if (placeholder === 'skeleton') {
      return (
        <Skeleton 
          className={`absolute inset-0 w-full h-full transition-opacity duration-300 ${
            isLoaded ? 'opacity-0' : 'opacity-100'
          }`}
        />
      );
    }
    
    return null;
  };

  // Render error state
  const renderError = () => (
    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
      <div className="text-center text-gray-400">
        <ImageOff className="w-8 h-8 mx-auto mb-2" />
        <p className="text-sm">Failed to load image</p>
        <p className="text-xs mt-1">{src}</p>
      </div>
    </div>
  );

  // Container styles
  const containerStyle: React.CSSProperties = {
    width: width ? `${width}px` : '100%',
    height: height ? `${height}px` : 'auto',
    aspectRatio: width && height ? `${width}/${height}` : undefined
  };

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={containerStyle}
    >
      {/* Placeholder */}
      {!isError && renderPlaceholder()}
      
      {/* Error state */}
      {isError && renderError()}
      
      {/* Main image */}
      {!isError && currentSrc && (
        <img
          ref={setRefs}
          src={currentSrc}
          srcSet={srcSet}
          sizes={sizes}
          alt={alt}
          className={`w-full h-full object-${objectFit} transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          loading={lazy && !priority ? 'lazy' : 'eager'}
          decoding="async"
          onLoad={() => {
            setIsLoaded(true);
            onLoad?.();
          }}
          onError={() => {
            setIsError(true);
            onError?.();
          }}
        />
      )}
      
      {/* Loading indicator for priority images */}
      {priority && !isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

// Higher-order component for image optimization
export const withImageOptimization = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.memo((props: P) => {
    return <Component {...props} />;
  });
};

// Image preloader utility
export const preloadImages = (urls: string[], options?: {
  width?: number;
  height?: number;
  quality?: number;
}) => {
  return Promise.all(
    urls.map(url => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        
        const optimizedUrl = optimizeImageUrl(
          url, 
          options?.width, 
          options?.height, 
          options?.quality
        );
        img.src = optimizedUrl;
      });
    })
  );
};

// Image cache manager
class ImageCache {
  private static cache = new Map<string, HTMLImageElement>();
  private static maxSize = 50;

  static get(url: string): HTMLImageElement | null {
    return this.cache.get(url) || null;
  }

  static set(url: string, img: HTMLImageElement): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(url, img);
  }

  static clear(): void {
    this.cache.clear();
  }

  static getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      urls: Array.from(this.cache.keys())
    };
  }
}

export { ImageCache };
export default OptimizedImage;
