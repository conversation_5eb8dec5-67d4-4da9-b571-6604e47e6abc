import { useContext, useCallback } from 'react';
import { ThemeContext, type Theme } from '@/contexts/ThemeContext';

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  // Create a stable setTheme function with inline arrow function
  const setTheme = useCallback((theme: Theme) => {
    context.setTheme(theme);
  }, [context.setTheme]);
  
  return { 
    theme: context.theme,
    setTheme
  };
};

export default useTheme;