import { toast } from 'sonner';
import { EventEmitter } from '../utils/EventEmitter';
import { CryptoService } from './CryptoService';
import { WebRTCService } from './WebRTCService';
import { MOCK_IMAGES } from '../lib/constants';

export interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastSeen?: Date;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'gif';
  status: 'sending' | 'sent' | 'delivered' | 'read';
  replyTo?: string;
  reactions?: {
    emoji: string;
    userId: string;
    timestamp: Date;
  }[];
  attachments?: {
    id: string;
    type: 'image' | 'file' | 'audio' | 'video';
    url: string;
    name: string;
    size: number;
    thumbnail?: string;
  }[];
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: User[];
  lastMessage?: {
    content: string;
    timestamp: Date;
    isRead: boolean;
  };
  unreadCount: number;
  isTyping: { [userId: string]: boolean };
  isMuted: boolean;
  isPinned: boolean;
  createdAt: Date;
  updatedAt: Date;
  settings: {
    theme?: string;
    nickname?: { [userId: string]: string };
    emoji?: string;
  };
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface MessageReaction {
  messageId: string;
  emoji: string;
  userId: string;
  timestamp: Date;
}

class MessagingService extends EventEmitter {
  private ws: WebSocket | null = null;
  private cryptoService: CryptoService;
  private webRTCService: WebRTCService;
  private connectionStatus = {
    isConnected: false,
    lastPing: new Date(),
    reconnectAttempts: 0
  };
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private typingTimeout: NodeJS.Timeout | null = null;
  
  // Local storage
  private conversations = new Map<string, Conversation>();
  private messages = new Map<string, Message[]>();
  private users = new Map<string, User>();
  private typingUsers = new Map<string, Set<string>>();
  private currentUserId: string = 'current-user';

  constructor() {
    super();
    this.cryptoService = new CryptoService();
    this.webRTCService = new WebRTCService();
    this.setupEventListeners();
    this.initializeMockData();
  }

  private setupEventListeners() {
    // WebRTC events
    this.webRTCService.on('callIncoming', (data) => {
      this.emit('callIncoming', data);
    });

    this.webRTCService.on('callStarted', (data) => {
      this.emit('callStarted', data);
    });

    this.webRTCService.on('callEnded', (data) => {
      this.emit('callEnded', data);
    });

    this.webRTCService.on('sendSignal', (signal) => {
      this.sendSignal(signal);
    });

    // Handle page visibility for connection management
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseConnection();
      } else {
        this.resumeConnection();
      }
    });

    // Handle network status
    window.addEventListener('online', () => {
      this.connect();
    });

    window.addEventListener('offline', () => {
      this.disconnect();
    });
  }

  private pauseConnection() {
    // Pause heartbeat and reduce activity when page is hidden
    this.stopHeartbeat();
  }

  private resumeConnection() {
    // Resume connection when page becomes visible
    if (this.connectionStatus.isConnected) {
      this.startHeartbeat();
    } else {
      this.connect();
    }
  }

  private connect() {
    this.connectToServer();
  }

  private initializeMockData() {
    // Mock conversations for demo
    const mockConversations: Conversation[] = [
      {
        id: 'conv-1',
        type: 'direct',
        participants: [
          {
            id: 'user-1',
            name: 'Sarah Johnson',
            avatar: MOCK_IMAGES.AVATARS[0],
            isOnline: true
          },
          {
            id: this.currentUserId,
            name: 'You',
            avatar: MOCK_IMAGES.AVATARS[6],
            isOnline: true
          }
        ],
        lastMessage: {
          content: 'Hey! How are you doing?',
          timestamp: new Date(Date.now() - 3600000),
          isRead: false
        },
        unreadCount: 2,
        isTyping: {},
        isMuted: false,
        isPinned: false,
        createdAt: new Date(Date.now() - 86400000),
        updatedAt: new Date(),
        settings: {}
      },
      {
        id: 'conv-2',
        type: 'group',
        name: 'Team Chat',
        participants: [
          {
            id: 'user-2',
            name: 'Mike Chen',
            avatar: MOCK_IMAGES.AVATARS[1],
            isOnline: false,
            lastSeen: new Date(Date.now() - 1800000)
          },
          {
            id: 'user-3',
            name: 'Emma Davis',
            avatar: MOCK_IMAGES.AVATARS[2],
            isOnline: true
          },
          {
            id: this.currentUserId,
            name: 'You',
            avatar: MOCK_IMAGES.AVATARS[6],
            isOnline: true
          }
        ],
        lastMessage: {
          content: 'Let\'s meet tomorrow at 10 AM',
          timestamp: new Date(Date.now() - 7200000),
          isRead: true
        },
        unreadCount: 0,
        isTyping: {},
        isMuted: false,
        isPinned: true,
        createdAt: new Date(Date.now() - 604800000),
        updatedAt: new Date(Date.now() - 7200000),
        settings: {}
      }
    ];

    mockConversations.forEach(conv => {
      this.conversations.set(conv.id, conv);
    });

    // Mock messages
    const mockMessages: Message[] = [
      {
        id: 'msg-1',
        conversationId: 'conv-1',
        senderId: 'user-1',
        content: 'Hey! How are you doing?',
        timestamp: new Date(Date.now() - 3600000),
        type: 'text',
        status: 'read'
      },
      {
        id: 'msg-2',
        conversationId: 'conv-1',
        senderId: this.currentUserId,
        content: 'I\'m doing great! Thanks for asking.',
        timestamp: new Date(Date.now() - 3000000),
        type: 'text',
        status: 'read'
      },
      {
        id: 'msg-3',
        conversationId: 'conv-1',
        senderId: 'user-1',
        content: 'That\'s awesome! Want to grab coffee later?',
        timestamp: new Date(Date.now() - 1800000),
        type: 'text',
        status: 'delivered'
      }
    ];

    this.messages.set('conv-1', mockMessages.filter(m => m.conversationId === 'conv-1'));

    const groupMessages: Message[] = [
      {
        id: 'msg-4',
        conversationId: 'conv-2',
        senderId: 'user-2',
        content: 'Let\'s meet tomorrow at 10 AM',
        timestamp: new Date(Date.now() - 7200000),
        type: 'text',
        status: 'read'
      },
      {
        id: 'msg-5',
        conversationId: 'conv-2',
        senderId: 'user-3',
        content: 'Sounds good to me!',
        timestamp: new Date(Date.now() - 7000000),
        type: 'text',
        status: 'read'
      }
    ];

    this.messages.set('conv-2', groupMessages);
  }

  // Connection management
  async connectToServer(wsUrl: string = 'ws://localhost:8080/messages'): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.connectionStatus.isConnected = true;
        this.connectionStatus.reconnectAttempts = 0;
        this.startHeartbeat();
        this.emit('connected');
      };

      this.ws.onmessage = (event) => {
        this.handleEncryptedMessage(JSON.parse(event.data));
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.connectionStatus.isConnected = false;
        this.stopHeartbeat();
        this.emit('disconnected');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('error', error);
      };

    } catch (error) {
      console.error('Failed to connect:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.connectionStatus.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts - 1);
    
    this.reconnectTimer = setTimeout(() => {
      console.log(`Reconnect attempt ${this.connectionStatus.reconnectAttempts}`);
      this.connectToServer();
    }, delay);
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000);
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Message handling
  private async handleEncryptedMessage(data: any) {
    switch (data.type) {
      case 'message':
        await this.handleIncomingEncryptedMessage(data.payload);
        break;
      case 'messageStatus':
        this.handleMessageStatusUpdate(data.payload);
        break;
      case 'typing':
        this.handleTypingIndicator(data.payload);
        break;
      case 'userStatus':
        this.handleUserStatusUpdate(data.payload);
        break;
      case 'conversationUpdate':
        this.handleConversationUpdate(data.payload);
        break;
      case 'callSignal':
        this.webRTCService.handleSignal(data.payload);
        break;
      case 'reaction':
        this.handleMessageReaction(data.payload);
        break;
      case 'pong':
        this.connectionStatus.lastPing = new Date();
        break;
      default:
        console.warn('Unknown message type:', data.type);
    }
  }

  private async handleIncomingEncryptedMessage(encryptedMessage: any) {
    try {
      // Decrypt the message
      const decryptedContent = await this.cryptoService.decryptMessage(
        encryptedMessage.content,
        encryptedMessage.conversationId
      );

      const message: Message = {
        ...encryptedMessage,
        content: decryptedContent,
        timestamp: new Date(encryptedMessage.timestamp)
      };

      // Store message
      const conversationMessages = this.messages.get(message.conversationId) || [];
      conversationMessages.push(message);
      this.messages.set(message.conversationId, conversationMessages);

      // Update conversation last message
      const conversation = this.conversations.get(message.conversationId);
      if (conversation) {
        conversation.lastMessage = {
          content: message.content,
          timestamp: message.timestamp,
          isRead: false
        };
        conversation.unreadCount = (conversation.unreadCount || 0) + 1;
        conversation.updatedAt = message.timestamp;
        this.conversations.set(message.conversationId, conversation);
      }

      this.emit('messageReceived', message);
      
      // Show notification for new messages
      if (message.senderId !== this.currentUserId) {
        const sender = conversation?.participants.find(p => p.id === message.senderId);
        toast.info(`New message from ${sender?.name || 'Unknown'}`);
      }
    } catch (error) {
      console.error('Failed to decrypt message:', error);
    }
  }

  private handleTypingIndicator(typing: TypingIndicator) {
    const conversation = this.conversations.get(typing.conversationId);
    if (conversation) {
      conversation.isTyping[typing.userId] = typing.isTyping;
      this.conversations.set(typing.conversationId, conversation);
      this.emit('typingUpdated', typing);
    }
  }

  private handleMessageReaction(reaction: MessageReaction) {
    // Find and update the message
    for (const [conversationId, messages] of this.messages.entries()) {
      const messageIndex = messages.findIndex(m => m.id === reaction.messageId);
      if (messageIndex !== -1) {
        const message = messages[messageIndex];
        if (!message.reactions) {
          message.reactions = [];
        }
        
        // Remove existing reaction from this user for this emoji
        message.reactions = message.reactions.filter(
          r => !(r.userId === reaction.userId && r.emoji === reaction.emoji)
        );
        
        // Add new reaction
        message.reactions.push({
          emoji: reaction.emoji,
          userId: reaction.userId,
          timestamp: reaction.timestamp
        });
        
        this.messages.set(conversationId, messages);
        this.emit('messageReactionUpdated', message);
        break;
      }
    }
  }

  private handleMessageStatusUpdate(data: { messageId: string; status: Message['status'] }) {
    // Find and update message status
    for (const [conversationId, messages] of this.messages.entries()) {
      const messageIndex = messages.findIndex(m => m.id === data.messageId);
      if (messageIndex !== -1) {
        messages[messageIndex].status = data.status;
        this.messages.set(conversationId, messages);
        this.emit('messageStatusUpdated', messages[messageIndex]);
        break;
      }
    }
  }

  private handleUserStatusUpdate(data: { userId: string; isOnline: boolean; lastSeen?: Date }) {
    // Update user status in all conversations
    for (const [conversationId, conversation] of this.conversations.entries()) {
      const participantIndex = conversation.participants.findIndex(p => p.id === data.userId);
      if (participantIndex !== -1) {
        conversation.participants[participantIndex].isOnline = data.isOnline;
        if (data.lastSeen) {
          conversation.participants[participantIndex].lastSeen = data.lastSeen;
        }
        this.conversations.set(conversationId, conversation);
      }
    }
    
    this.emit('userStatusUpdated', data);
  }

  private handleConversationUpdate(conversation: Conversation) {
    this.conversations.set(conversation.id, conversation);
    this.emit('conversationUpdated', conversation);
  }

  // Public API methods - Encrypted messaging
  async sendEncryptedMessage(conversationId: string, content: string, type: Message['type'] = 'text', attachments?: Message['attachments']): Promise<Message> {
    if (!this.connectionStatus.isConnected) {
      // Fallback to non-encrypted for demo
      return this.sendMessage(conversationId, content, type, attachments);
    }

    try {
      // Encrypt the message
      const encryptedContent = await this.cryptoService.encryptMessage(content, conversationId);
      
      const message: Message = {
        id: this.generateMessageId(),
        conversationId,
        senderId: this.currentUserId,
        content: encryptedContent,
        timestamp: new Date(),
        type,
        status: 'sending',
        attachments
      };

      // Store locally first (with decrypted content)
      const localMessage = { ...message, content };
      const conversationMessages = this.messages.get(conversationId) || [];
      conversationMessages.push(localMessage);
      this.messages.set(conversationId, conversationMessages);

      // Update conversation
      const conversation = this.conversations.get(conversationId);
      if (conversation) {
        conversation.lastMessage = {
          content,
          timestamp: message.timestamp,
          isRead: true
        };
        conversation.updatedAt = message.timestamp;
        this.conversations.set(conversationId, conversation);
      }

      // Send encrypted to server
      this.ws?.send(JSON.stringify({
        type: 'message',
        payload: message
      }));

      this.emit('messageSent', localMessage);
      return localMessage;
    } catch (error) {
      console.error('Failed to send encrypted message:', error);
      throw error;
    }
  }

  // Public API methods - Standard messaging
  public sendMessage(conversationId: string, content: string, type: Message['type'] = 'text', attachments?: Message['attachments']): Message {
    const message: Message = {
      id: this.generateMessageId(),
      conversationId,
      senderId: this.currentUserId,
      content,
      timestamp: new Date(),
      type,
      status: 'sending',
      attachments
    };

    // Add to local messages immediately
    const conversationMessages = this.messages.get(conversationId) || [];
    conversationMessages.push(message);
    this.messages.set(conversationId, conversationMessages);

    // Update conversation
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.lastMessage = {
        content,
        timestamp: message.timestamp,
        isRead: true
      };
      conversation.updatedAt = message.timestamp;
      this.conversations.set(conversationId, conversation);
    }

    // Emit to listeners
    this.emit('messageSent', message);

    // Send via WebSocket or simulate
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message',
        payload: message
      }));
    } else {
      // Simulate message delivery
      setTimeout(() => {
        message.status = 'delivered';
        this.emit('messageStatusUpdated', message);
      }, 1000);
    }

    return message;
  }

  public sendTypingIndicator(conversationId: string, isTyping: boolean) {
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'typing',
        payload: {
          conversationId,
          userId: this.currentUserId,
          isTyping,
          timestamp: new Date()
        }
      }));
    }

    if (isTyping) {
      // Auto-stop typing after 3 seconds
      this.typingTimeout = setTimeout(() => {
        this.sendTypingIndicator(conversationId, false);
      }, 3000);
    }
  }

  public markAsRead(conversationId: string, messageId: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'markRead',
        payload: {
          conversationId,
          messageId,
          userId: this.currentUserId,
          timestamp: new Date()
        }
      }));
    }

    // Update local conversation
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.unreadCount = 0;
      this.conversations.set(conversationId, conversation);
      this.emit('conversationUpdated', conversation);
    }
  }

  public addReaction(messageId: string, emoji: string) {
    const reaction: MessageReaction = {
      messageId,
      emoji,
      userId: this.currentUserId,
      timestamp: new Date()
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'reaction',
        payload: reaction
      }));
    } else {
      // Simulate locally
      this.handleMessageReaction(reaction);
    }
  }

  public removeReaction(messageId: string, emoji: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'removeReaction',
        payload: {
          messageId,
          emoji,
          userId: this.currentUserId,
          timestamp: new Date()
        }
      }));
    }
  }

  public deleteMessage(messageId: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'deleteMessage',
        payload: {
          messageId,
          userId: this.currentUserId,
          timestamp: new Date()
        }
      }));
    }
  }

  public editMessage(messageId: string, newContent: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'editMessage',
        payload: {
          messageId,
          newContent,
          userId: this.currentUserId,
          timestamp: new Date()
        }
      }));
    }
  }

  public createConversation(participants: User[], type: 'direct' | 'group' = 'direct', name?: string): Conversation {
    const conversation: Conversation = {
      id: this.generateConversationId(),
      type,
      name,
      participants,
      unreadCount: 0,
      isTyping: {},
      isMuted: false,
      isPinned: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      settings: {}
    };

    this.conversations.set(conversation.id, conversation);
    this.messages.set(conversation.id, []);
    
    this.emit('conversationCreated', conversation);
    return conversation;
  }

  // Video Call Methods
  async initiateCall(conversationId: string, type: 'audio' | 'video'): Promise<void> {
    try {
      await this.webRTCService.initiateCall(conversationId, type);
      
      // Notify other participants
      this.ws?.send(JSON.stringify({
        type: 'callSignal',
        payload: {
          conversationId,
          action: 'initiate',
          callType: type,
          timestamp: new Date()
        }
      }));
    } catch (error) {
      console.error('Failed to initiate call:', error);
      throw error;
    }
  }

  async acceptCall(conversationId: string): Promise<void> {
    try {
      await this.webRTCService.acceptCall(conversationId);
    } catch (error) {
      console.error('Failed to accept call:', error);
      throw error;
    }
  }

  async rejectCall(conversationId: string): Promise<void> {
    this.webRTCService.rejectCall(conversationId);
  }

  async endCall(conversationId: string): Promise<void> {
    this.webRTCService.endCall(conversationId);
  }

  // Utility methods
  private sendSignal(signal: any): void {
    if (this.connectionStatus.isConnected) {
      this.ws?.send(JSON.stringify({
        type: 'callSignal',
        payload: signal
      }));
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  public getConversations(): Conversation[] {
    return Array.from(this.conversations.values())
      .sort((a, b) => {
        // Pinned conversations first
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        
        // Then by last update
        return b.updatedAt.getTime() - a.updatedAt.getTime();
      });
  }

  public getMessages(conversationId: string): Message[] {
    return this.messages.get(conversationId) || [];
  }

  public getConversation(conversationId: string): Conversation | undefined {
    return this.conversations.get(conversationId);
  }

  public getConnectionStatus() {
    return { ...this.connectionStatus };
  }

  public getCurrentUserId(): string {
    return this.currentUserId;
  }

  public setCurrentUserId(userId: string) {
    this.currentUserId = userId;
  }

  // Cleanup
  public disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = null;
    }

    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.connectionStatus.isConnected = false;
  }

  public destroy() {
    this.disconnect();
    this.webRTCService.destroy();
    this.cryptoService.destroy();
    this.removeAllListeners();
  }
}

export const messagingService = new MessagingService();
export default MessagingService;